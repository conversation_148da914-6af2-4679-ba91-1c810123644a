'use client'

import { usePathname } from 'next/navigation'
import { SidebarProvider } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { useAuth } from '@/lib/auth-context'
import { KeyboardShortcutsHelpFloating } from '@/components/keyboard-shortcuts-help'

interface LayoutWrapperProps {
  children: React.ReactNode
}

export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const pathname = usePathname()
  const { user, isLoading } = useAuth()

  // Define public routes that don't need sidebar
  const publicRoutes = [
    '/',
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password'
  ]

  // Check if current route is public
  const isPublicRoute = publicRoutes.includes(pathname) || pathname.startsWith('/profile/')

  // For authenticated routes, always show sidebar if user is authenticated
  // For public routes, never show sidebar
  const shouldShowSidebar = !isPublicRoute && user && !isLoading

  if (shouldShowSidebar) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <main className="flex-1 overflow-hidden">{children}</main>
        <KeyboardShortcutsHelpFloating />
      </SidebarProvider>
    )
  }

  // For public routes or unauthenticated users, render without sidebar
  return (
    <>
      {children}
      <KeyboardShortcutsHelpFloating />
    </>
  )
}
