'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Loader2, Save, User, Shield, Key, Download, Trash2, <PERSON>, <PERSON>fresh<PERSON><PERSON>, CheckCircle, AlertCircle, Eye, EyeOff, MessageSquare, ExternalLink, Clock } from 'lucide-react'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/hooks/use-toast'
import { authenticatedFetch } from '@/lib/utils'

interface UserProfile {
  id: string
  email: string
  displayName: string
  role: string
  emailVerified: boolean
  isActive: boolean
  preferences: Record<string, any>
  privacySettings: Record<string, any>
  createdAt: string
  lastLogin?: string
}

export function UserSettings() {
  const { user, refreshUser } = useAuth()
  const { toast } = useToast()

  // Helper function to check if a string is a masked API key (all asterisks)
  const isMaskedApiKey = (value: string): boolean => {
    return /^\*+$/.test(value)
  }

  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')

  // Form states
  const [displayName, setDisplayName] = useState('')
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  // Privacy settings
  const [profilePublic, setProfilePublic] = useState(false)
  const [allowDataCollection, setAllowDataCollection] = useState(true)
  const [emailNotifications, setEmailNotifications] = useState(true)

  // Zotero settings
  const [zoteroApiKey, setZoteroApiKey] = useState('')
  const [hasStoredApiKey, setHasStoredApiKey] = useState(false)
  const [zoteroLibraryType, setZoteroLibraryType] = useState<'user' | 'group'>('user')
  const [zoteroLibraryId, setZoteroLibraryId] = useState('')
  const [zoteroEnabled, setZoteroEnabled] = useState(false)
  const [zoteroLibraries, setZoteroLibraries] = useState<any[]>([])
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [isLoadingLibraries, setIsLoadingLibraries] = useState(false)
  const [showApiKey, setShowApiKey] = useState(false)
  const [isLoadingApiKey, setIsLoadingApiKey] = useState(false)
  const [isSyncingAll, setIsSyncingAll] = useState(false)
  const [syncResults, setSyncResults] = useState<{
    successful: string[]
    failed: Array<{ paperId: string; error: string }>
    total: number
  } | null>(null)

  // Data export state
  const [isExporting, setIsExporting] = useState(false)

  // Account deletion state
  const [isDeletingAccount, setIsDeletingAccount] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [deleteConfirmationText, setDeleteConfirmationText] = useState('')

  useEffect(() => {
    fetchProfile()
  }, [])

  // Auto-load libraries when switching to group type and API key is available
  useEffect(() => {
    if (zoteroLibraryType === 'group' && zoteroApiKey && zoteroApiKey !== '***' && zoteroApiKey.trim() !== '') {
      loadZoteroLibraries()
    }
  }, [zoteroLibraryType, zoteroApiKey])

  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (response.ok) {
        let data
        try {
          data = await response.json()
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError)
          setError('Invalid response from server')
          return
        }

        setProfile(data)
        setDisplayName(data.displayName || '')

        // Set privacy settings from profile
        const privacy = data.privacySettings || {}
        setProfilePublic(privacy.profilePublic || false)
        setAllowDataCollection(privacy.allowDataCollection !== false)
        setEmailNotifications(privacy.emailNotifications !== false)

        // Set Zotero settings from profile
        const zotero = data.preferences?.zotero || {}
        const hasApiKey = !!zotero.apiKey
        setHasStoredApiKey(hasApiKey)
        // Use the actual masked API key from server (preserves length)
        setZoteroApiKey(zotero.apiKey || '')
        setZoteroLibraryType(zotero.libraryType || 'user')
        setZoteroLibraryId(zotero.libraryId || '')
        setZoteroEnabled(zotero.enabled || false)
      } else {
        setError('Failed to load profile')
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
      setError('Network error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError('')

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          displayName,
          privacySettings: {
            profilePublic,
            // Note: allowDataCollection and emailNotifications are coming soon
          },
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setProfile(data)
        await refreshUser()
        toast({
          title: 'Profile updated',
          description: 'Your profile has been updated successfully.',
        })
      } else {
        setError(data.error || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      setError('Network error')
    } finally {
      setIsSaving(false)
    }
  }

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()

    if (newPassword !== confirmPassword) {
      setError('New passwords do not match')
      return
    }

    if (newPassword.length < 8) {
      setError('New password must be at least 8 characters long')
      return
    }

    setIsSaving(true)
    setError('')

    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setCurrentPassword('')
        setNewPassword('')
        setConfirmPassword('')
        toast({
          title: 'Password changed',
          description: 'Your password has been changed successfully.',
        })
      } else {
        setError(data.error || 'Failed to change password')
      }
    } catch (error) {
      console.error('Error changing password:', error)
      setError('Network error')
    } finally {
      setIsSaving(false)
    }
  }

  const handleZoteroSettingsUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError('')

    try {
      const response = await authenticatedFetch('/api/user/zotero-settings', {
        method: 'PUT',
        body: JSON.stringify({
          apiKey: zoteroApiKey,
          libraryType: zoteroLibraryType,
          libraryId: zoteroLibraryId,
          enabled: zoteroEnabled,
        }),
      })

      let data
      try {
        data = await response.json()
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError)
        setError('Invalid response from server')
        setConnectionStatus('error')
        return
      }

      if (response.ok) {
        // Check connection test result
        const connectionTest = data.data?.connectionTest

        if (connectionTest === true) {
          toast({
            title: 'Zotero settings updated',
            description: 'Your Zotero settings have been updated and connection test was successful.',
          })
          setConnectionStatus('success')
        } else if (connectionTest === false) {
          toast({
            title: 'Zotero settings updated',
            description: 'Settings saved, but connection test failed. Please verify your API key.',
            variant: 'destructive'
          })
          setConnectionStatus('error')
        } else {
          toast({
            title: 'Zotero settings updated',
            description: 'Your Zotero settings have been updated successfully.',
          })
          setConnectionStatus('success')
        }
      } else {
        setError(data.error || 'Failed to update Zotero settings')
        setConnectionStatus('error')
      }
    } catch (error) {
      console.error('Error updating Zotero settings:', error)
      setError('Network error')
      setConnectionStatus('error')
    } finally {
      setIsSaving(false)
    }
  }

  const testZoteroConnection = async () => {
    if (!zoteroApiKey || isMaskedApiKey(zoteroApiKey)) {
      setError('Please enter a valid API key')
      return
    }

    setIsTestingConnection(true)
    setError('')

    try {
      const response = await authenticatedFetch('/api/user/zotero-settings', {
        method: 'PUT',
        body: JSON.stringify({
          apiKey: zoteroApiKey,
          libraryType: zoteroLibraryType,
          libraryId: zoteroLibraryId,
          enabled: false, // Just test, don't enable yet
        }),
      })

      let data
      try {
        data = await response.json()
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError)
        setConnectionStatus('error')
        setError('Invalid response from server')
        return
      }

      if (response.ok) {
        setConnectionStatus('success')
        toast({
          title: 'Connection successful',
          description: 'Successfully connected to Zotero.',
        })
        // Load libraries after successful connection
        loadZoteroLibraries()
      } else {
        setConnectionStatus('error')
        setError(data.error || 'Failed to connect to Zotero')
      }
    } catch (error) {
      console.error('Error testing Zotero connection:', error)
      setConnectionStatus('error')
      setError('Network error')
    } finally {
      setIsTestingConnection(false)
    }
  }

  const loadZoteroLibraries = async () => {
    setIsLoadingLibraries(true)
    setError('')
    try {
      const response = await authenticatedFetch('/api/user/zotero-libraries')
      if (response.ok) {
        const data = await response.json()
        setZoteroLibraries(data.data || [])
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        const errorMessage = errorData.error || `Failed to load Zotero libraries (${response.status})`
        console.error('Failed to load Zotero libraries:', errorMessage)
        setError(`Failed to load libraries: ${errorMessage}`)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error'
      console.error('Error loading Zotero libraries:', error)
      setError(`Error loading libraries: ${errorMessage}`)
    } finally {
      setIsLoadingLibraries(false)
    }
  }

  const toggleApiKeyVisibility = async () => {
    if (!showApiKey && hasStoredApiKey && isMaskedApiKey(zoteroApiKey)) {
      // Need to fetch the actual API key from the server
      setIsLoadingApiKey(true)
      try {
        const response = await authenticatedFetch('/api/user/zotero-settings?showActual=true')
        if (response.ok) {
          const data = await response.json()
          if (data.data && data.data.apiKey && !isMaskedApiKey(data.data.apiKey)) {
            setZoteroApiKey(data.data.apiKey)
          }
        }
      } catch (error) {
        console.error('Error fetching actual API key:', error)
        setError('Failed to fetch API key')
        return
      } finally {
        setIsLoadingApiKey(false)
      }
    } else if (showApiKey && hasStoredApiKey) {
      // Hide the key - return to masked state
      setIsLoadingApiKey(true)
      try {
        const response = await authenticatedFetch('/api/user/zotero-settings')
        if (response.ok) {
          const data = await response.json()
          if (data.data && data.data.apiKey) {
            setZoteroApiKey(data.data.apiKey) // This will be the masked version
          }
        }
      } finally {
        setIsLoadingApiKey(false)
      }
    }
    setShowApiKey(!showApiKey)
  }

  const syncAllChangedPapers = async () => {
    setIsSyncingAll(true)
    setSyncResults(null)
    setError('')

    try {
      const response = await authenticatedFetch('/api/papers/sync-zotero', {
        method: 'POST',
        body: JSON.stringify({
          mode: 'changed'
        }),
      })

      let data
      try {
        data = await response.json()
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError)
        throw new Error('Invalid response format from server')
      }

      if (response.ok) {
        const results = data.data
        setSyncResults(results)

        toast({
          title: 'Sync completed',
          description: `${results.successful.length} papers synced successfully, ${results.failed.length} failed`,
        })
      } else {
        const errorMessage = data.errors?.[0] || data.error || 'Failed to sync papers to Zotero'
        setError(errorMessage)
        toast({
          title: 'Sync failed',
          description: errorMessage,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error syncing papers to Zotero:', error)
      setError('Network error occurred while syncing to Zotero')
      toast({
        title: 'Sync failed',
        description: 'Network error occurred while syncing to Zotero',
        variant: 'destructive'
      })
    } finally {
      setIsSyncingAll(false)
    }
  }

  const handleDataExport = async () => {
    setIsExporting(true)
    setError('')

    try {
      const response = await authenticatedFetch('/api/user/export')

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to export data')
      }

      // Get the blob data
      const blob = await response.blob()

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Get filename from Content-Disposition header or use default
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = 'papernugget-export.json'
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }

      link.download = filename
      document.body.appendChild(link)
      link.click()

      // Cleanup
      window.URL.revokeObjectURL(url)
      document.body.removeChild(link)

      toast({
        title: 'Export successful',
        description: 'Your data has been exported and downloaded.',
      })
    } catch (error) {
      console.error('Error exporting data:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to export data'
      setError(errorMessage)
      toast({
        title: 'Export failed',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleAccountDeletion = async () => {
    if (deleteConfirmationText !== 'DELETE') {
      setError('Please type "DELETE" to confirm account deletion')
      return
    }

    setIsDeletingAccount(true)
    setError('')

    try {
      const response = await authenticatedFetch('/api/user/delete-account', {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete account')
      }

      const result = await response.json()

      toast({
        title: 'Account deletion initiated',
        description: 'Your account deletion is in progress. You will receive an email confirmation shortly.',
      })

      // Close the confirmation dialog
      setShowDeleteConfirmation(false)
      setDeleteConfirmationText('')

      // Redirect to landing page after a short delay
      setTimeout(() => {
        window.location.href = '/'
      }, 2000)

    } catch (error) {
      console.error('Error deleting account:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete account'
      setError(errorMessage)
      toast({
        title: 'Account deletion failed',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setIsDeletingAccount(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!profile) {
    return (
      <Alert variant="destructive">
        <AlertDescription>Failed to load user profile</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Account Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile">
            <User className="mr-2 h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="security">
            <Key className="mr-2 h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="privacy">
            <Shield className="mr-2 h-4 w-4" />
            Privacy
          </TabsTrigger>
          <TabsTrigger value="zotero">
            <Link className="mr-2 h-4 w-4" />
            Zotero
          </TabsTrigger>
          <TabsTrigger value="data">
            <Download className="mr-2 h-4 w-4" />
            Data
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information and display preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profile.email}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-muted-foreground">
                      Email cannot be changed. Contact support if needed.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="displayName">Display Name</Label>
                    <Input
                      id="displayName"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      placeholder="Enter your display name"
                      disabled={isSaving}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Account Status</Label>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${profile.isActive ? 'bg-green-500' : 'bg-red-500'}`} />
                      <span className="text-sm">
                        {profile.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Email Verification</Label>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${profile.emailVerified ? 'bg-green-500' : 'bg-yellow-500'}`} />
                      <span className="text-sm">
                        {profile.emailVerified ? 'Verified' : 'Pending verification'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Role</Label>
                    <Input value={profile.role} disabled className="bg-muted" />
                  </div>

                  <div className="space-y-2">
                    <Label>Member Since</Label>
                    <Input
                      value={new Date(profile.createdAt).toLocaleDateString()}
                      disabled
                      className="bg-muted"
                    />
                  </div>
                </div>

                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>
                Update your password to keep your account secure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordChange} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    disabled={isSaving}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    disabled={isSaving}
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Password must be at least 8 characters long
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    disabled={isSaving}
                    required
                  />
                </div>

                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Changing Password...
                    </>
                  ) : (
                    <>
                      <Key className="mr-2 h-4 w-4" />
                      Change Password
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Security Information</CardTitle>
              <CardDescription>
                View your account security details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Last Login</p>
                  <p className="text-sm text-muted-foreground">
                    {profile.lastLogin
                      ? new Date(profile.lastLogin).toLocaleString()
                      : 'Never'
                    }
                  </p>
                </div>
              </div>

              <Separator />

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Two-Factor Authentication</p>
                  <p className="text-sm text-muted-foreground">
                    Add an extra layer of security to your account
                  </p>
                </div>
                <Button variant="outline" disabled>
                  Coming Soon
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="privacy" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Privacy Settings</CardTitle>
              <CardDescription>
                Control how your data is used and shared
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Public Profile</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow others to see your profile information and papers
                    </p>
                  </div>
                  <Switch
                    checked={profilePublic}
                    onCheckedChange={setProfilePublic}
                  />
                </div>

                {profilePublic && profile && (
                  <div className="p-4 bg-muted/50 rounded-lg space-y-2">
                    <Label className="text-sm font-medium">Your Public Profile URL</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        value={`${window.location.origin}/profile/${profile.id}`}
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(`${window.location.origin}/profile/${profile.id}`)
                          toast({
                            title: 'Copied!',
                            description: 'Profile URL copied to clipboard',
                          })
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Share this URL to let others view your public profile and papers
                    </p>
                  </div>
                )}
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="flex items-center gap-2">
                    <Label className="text-muted-foreground">Data Collection</Label>
                    <Badge variant="secondary" className="text-xs">
                      <Clock className="mr-1 h-3 w-3" />
                      Coming Soon
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Allow collection of usage data to improve the service
                  </p>
                </div>
                <Switch
                  checked={false}
                  disabled={true}
                  className="opacity-50"
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="flex items-center gap-2">
                    <Label className="text-muted-foreground">Email Notifications</Label>
                    <Badge variant="secondary" className="text-xs">
                      <Clock className="mr-1 h-3 w-3" />
                      Coming Soon
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Receive email notifications about your account
                  </p>
                </div>
                <Switch
                  checked={false}
                  disabled={true}
                  className="opacity-50"
                />
              </div>

              <Button onClick={handleProfileUpdate} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Privacy Settings
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="zotero" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Zotero Integration</CardTitle>
              <CardDescription>
                Connect your Zotero account to sync papers and notes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleZoteroSettingsUpdate} className="space-y-6">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable Zotero Sync</Label>
                    <p className="text-sm text-muted-foreground">
                      Sync your papers and notes to Zotero
                    </p>
                  </div>
                  <Switch
                    checked={zoteroEnabled}
                    onCheckedChange={setZoteroEnabled}
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="zoteroApiKey">Zotero API Key</Label>
                    <div className="flex gap-2">
                      <div className="relative flex-1">
                        <Input
                          id="zoteroApiKey"
                          type={showApiKey ? "text" : "password"}
                          value={zoteroApiKey}
                          onChange={(e) => {
                            const newValue = e.target.value
                            setZoteroApiKey(newValue)
                            // If user starts typing and it's not the masked value, reset stored key flag
                            if (!isMaskedApiKey(newValue) && newValue !== '') {
                              setHasStoredApiKey(false)
                            }
                          }}
                          placeholder={hasStoredApiKey && isMaskedApiKey(zoteroApiKey) ? "API key is stored (clear to enter new)" : "Enter your Zotero API key"}
                          disabled={isSaving || isTestingConnection}
                          className="pr-10"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={toggleApiKeyVisibility}
                          disabled={isSaving || isTestingConnection || isLoadingApiKey}
                          title="Toggle API key visibility"
                        >
                          {isLoadingApiKey ? (
                            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                          ) : showApiKey ? (
                            <EyeOff className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <Eye className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={testZoteroConnection}
                        disabled={!zoteroApiKey || isMaskedApiKey(zoteroApiKey) || isTestingConnection}
                      >
                        {isTestingConnection ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : connectionStatus === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : connectionStatus === 'error' ? (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Get your API key from{' '}
                      <a
                        href="https://www.zotero.org/settings/keys"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        Zotero Settings
                      </a>
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="libraryType">Library Type</Label>
                    <Select
                      value={zoteroLibraryType}
                      onValueChange={(value) => setZoteroLibraryType(value as 'user' | 'group')}
                      disabled={isSaving}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select library type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">Personal Library</SelectItem>
                        <SelectItem value="group">Group Library</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {zoteroLibraryType === 'group' && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="libraryId">Group Library</Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={loadZoteroLibraries}
                          disabled={isLoadingLibraries || !zoteroApiKey || isMaskedApiKey(zoteroApiKey)}
                          className="h-6 px-2"
                        >
                          <RefreshCw className={`h-3 w-3 ${isLoadingLibraries ? 'animate-spin' : ''}`} />
                        </Button>
                      </div>
                      {isLoadingLibraries ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-sm text-muted-foreground">Loading libraries...</span>
                        </div>
                      ) : (
                        <>
                          {zoteroLibraries.filter(lib => lib.type === 'group').length === 0 ? (
                            <div className="text-sm text-muted-foreground p-2 border rounded">
                              No group libraries found. Make sure your API key has access to group libraries.
                            </div>
                          ) : (
                            <Select
                              value={zoteroLibraryId}
                              onValueChange={setZoteroLibraryId}
                              disabled={isSaving}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select a group library" />
                              </SelectTrigger>
                              <SelectContent>
                                {zoteroLibraries
                                  .filter(lib => lib.type === 'group')
                                  .map((library) => (
                                    <SelectItem key={library.id} value={library.id}>
                                      {library.name}
                                      {library.description && (
                                        <span className="text-xs text-muted-foreground ml-2">
                                          - {library.description}
                                        </span>
                                      )}
                                    </SelectItem>
                                  ))}
                              </SelectContent>
                            </Select>
                          )}
                        </>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Zotero Settings
                      </>
                    )}
                  </Button>

                  {zoteroEnabled && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={syncAllChangedPapers}
                      disabled={isSyncingAll || !zoteroEnabled}
                      className="text-purple-600 border-purple-200 hover:bg-purple-50"
                    >
                      {isSyncingAll ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Syncing...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Sync All Changed
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </form>

              {syncResults && (
                <div className="mt-6 p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Sync Results</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>{syncResults.successful.length} papers synced successfully</span>
                    </div>
                    {syncResults.failed.length > 0 && (
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                        <span>{syncResults.failed.length} papers failed to sync</span>
                      </div>
                    )}
                    <div className="text-muted-foreground">
                      Total: {syncResults.total} papers processed
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Data Management</CardTitle>
              <CardDescription>
                Export or delete your account data
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Export Your Data</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Download a copy of all your data including papers, notes, and collections in JSON format.
                  </p>
                  <Button
                    variant="outline"
                    onClick={handleDataExport}
                    disabled={isExporting}
                  >
                    {isExporting ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Download className="mr-2 h-4 w-4" />
                    )}
                    {isExporting ? 'Exporting...' : 'Export Data'}
                  </Button>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium text-destructive">Delete Account</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Permanently delete your account and all associated data. This action cannot be undone.
                  </p>
                  <Dialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
                    <DialogTrigger asChild>
                      <Button variant="destructive" disabled={isDeletingAccount}>
                        {isDeletingAccount ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="mr-2 h-4 w-4" />
                        )}
                        {isDeletingAccount ? 'Deleting Account...' : 'Delete Account'}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle className="text-destructive">Delete Account</DialogTitle>
                        <DialogDescription>
                          This action will permanently delete your account and all associated data including:
                          <ul className="list-disc list-inside mt-2 space-y-1">
                            <li>All your papers and notes</li>
                            <li>All your collections</li>
                            <li>All your review progress</li>
                            <li>Your user profile and settings</li>
                          </ul>
                          <br />
                          <strong>This action cannot be undone.</strong>
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="delete-confirmation" className="col-span-4">
                            Type <strong>DELETE</strong> to confirm:
                          </Label>
                          <Input
                            id="delete-confirmation"
                            value={deleteConfirmationText}
                            onChange={(e) => setDeleteConfirmationText(e.target.value)}
                            className="col-span-4"
                            placeholder="Type DELETE here"
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setShowDeleteConfirmation(false)
                            setDeleteConfirmationText('')
                            setError('')
                          }}
                          disabled={isDeletingAccount}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleAccountDeletion}
                          disabled={isDeletingAccount || deleteConfirmationText !== 'DELETE'}
                        >
                          {isDeletingAccount ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="mr-2 h-4 w-4" />
                          )}
                          {isDeletingAccount ? 'Deleting...' : 'Delete Account'}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Feedback Section */}
      <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/20">
        <CardContent className="pt-6">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <MessageSquare className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                Help us improve PaperNugget
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Found a bug or have a feature request? Share your feedback with our community and help shape the future of PaperNugget.
              </p>
              <div className="mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-blue-700 border-blue-300 hover:bg-blue-100 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-900"
                  onClick={() => window.open('https://papernugget.canny.io/make-paper-nugget-better', '_blank', 'noopener,noreferrer')}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Submit Feedback
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}