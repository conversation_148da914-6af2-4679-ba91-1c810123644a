"use client"

import { useState, useEffect } from "react"
import { Keyboard } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export function KeyboardShortcutsHelp() {
  const [open, setOpen] = useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Keyboard className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Keyboard Shortcuts</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <div>
            <h3 className="font-semibold mb-2">Papers List</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>New paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">N</kbd>
              </div>
              <div className="flex justify-between">
                <span>Clear filters</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
              </div>
              <div className="flex justify-between">
                <span>Star paper (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">S</kbd>
              </div>
              <div className="flex justify-between">
                <span>Edit paper (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
              </div>
              <div className="flex justify-between">
                <span>Delete paper (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Paper Details</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Star paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">S</kbd>
              </div>
              <div className="flex justify-between">
                <span>Add/remove from review</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
              </div>
              <div className="flex justify-between">
                <span>Edit (focus title)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
              </div>
              <div className="flex justify-between">
                <span>Back to papers</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
              </div>
              <div className="flex justify-between">
                <span>Delete paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
              </div>
              <div className="flex justify-between">
                <span>Focus bullet point</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">1-6</kbd>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Collections</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>New collection</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">N</kbd>
              </div>
              <div className="flex justify-between">
                <span>Present collection (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">P</kbd>
              </div>
              <div className="flex justify-between">
                <span>Delete collection (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Collection Details</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Edit collection name</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
              </div>
              <div className="flex justify-between">
                <span>Add/remove from review</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
              </div>
              <div className="flex justify-between">
                <span>Present collection</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">P</kbd>
              </div>
              <div className="flex justify-between">
                <span>Back to collections</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
              </div>
              <div className="flex justify-between">
                <span>Delete collection</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Review Page</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Mark as reviewed</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">G</kbd>
              </div>
              <div className="flex justify-between">
                <span>Mark for revisit</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
              </div>
              <div className="flex justify-between">
                <span>Previous paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">← / H</kbd>
              </div>
              <div className="flex justify-between">
                <span>Next paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">→ / L</kbd>
              </div>
              <div className="flex justify-between">
                <span>Exit review</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
              </div>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <p>💡 Tip: Use Tab to navigate between focusable elements, then use the shortcuts above.</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

/**
 * Floating keyboard shortcuts help button positioned in bottom-right corner
 */
export function KeyboardShortcutsHelpFloating() {
  const [open, setOpen] = useState(false)

  // Global keyboard shortcut to open help with "?" key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only trigger if not typing in an input/textarea and user presses "?"
      const target = e.target as HTMLElement
      const isTyping = target.tagName === "INPUT" || target.tagName === "TEXTAREA" || target.contentEditable === "true"

      if (!isTyping && e.key === "?" && !e.ctrlKey && !e.metaKey && !e.altKey) {
        e.preventDefault()
        setOpen(true)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Dialog open={open} onOpenChange={setOpen}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 bg-background/95 backdrop-blur-sm border-2"
                aria-label="Keyboard shortcuts help"
              >
                <Keyboard className="h-5 w-5" />
              </Button>
            </DialogTrigger>
          </TooltipTrigger>
          <TooltipContent side="left" className="mb-2">
            <p>Keyboard shortcuts (Press ?)</p>
          </TooltipContent>
        </Tooltip>

        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Keyboard Shortcuts</DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold mb-2">Papers List</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>New paper</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">N</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Clear filters</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Star paper (on focused card)</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">S</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Edit paper (on focused card)</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Delete paper (on focused card)</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Paper Details</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Star paper</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">S</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Add/remove from review</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Edit (focus title)</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Back to papers</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Delete paper</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Focus bullet point</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">1-6</kbd>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Collections</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>New collection</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">N</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Present collection (on focused card)</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">P</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Delete collection (on focused card)</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Collection Details</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Edit collection name</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Add/remove from review</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Present collection</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">P</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Back to collections</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Delete collection</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Review Page</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Mark as reviewed</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">G</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Mark for revisit</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Previous paper</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">← / H</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Next paper</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">→ / L</kbd>
                </div>
                <div className="flex justify-between">
                  <span>Exit review</span>
                  <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
                </div>
              </div>
            </div>

            <div className="text-xs text-muted-foreground">
              <p>💡 Tip: Use Tab to navigate between focusable elements, then use the shortcuts above.</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
