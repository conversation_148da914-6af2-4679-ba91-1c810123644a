"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ZoteroDestinationSelector } from "@/components/zotero-destination-selector"
import { authenticatedFetch } from "@/lib/utils"
import { Loader2, AlertTriangle } from "lucide-react"

interface ZoteroSyncDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  paperId: string
  paperTitle: string
  onSyncSuccess?: (result: { itemKey: string; noteKey: string; message: string }) => void
  onSyncError?: (error: string) => void
}

/**
 * Dialog for manual Zotero destination selection when automatic sync fails
 */
export function ZoteroSyncDialog({
  open,
  onOpenChange,
  paperId,
  paperTitle,
  onSyncSuccess,
  onSyncError
}: ZoteroSyncDialogProps) {
  const [selectedDestination, setSelectedDestination] = useState<{
    libraryType?: 'user' | 'group'
    libraryId?: string
  }>({ libraryType: 'user' })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Reset state when dialog opens
  useEffect(() => {
    if (open) {
      setSelectedDestination({ libraryType: 'user' })
      setError(null)
    }
  }, [open])

  const handleSync = async () => {
    if (!selectedDestination.libraryType) {
      setError('Please select a Zotero destination')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}/sync-zotero`, {
        method: 'POST',
        body: JSON.stringify({
          forceDestination: selectedDestination
        })
      })

      const data = await response.json()

      if (response.ok) {
        onSyncSuccess?.(data.data)
        onOpenChange(false)
      } else {
        const errorMessage = data.errors?.[0] || data.error || 'Failed to sync to Zotero'
        setError(errorMessage)
        onSyncError?.(errorMessage)
      }
    } catch (error) {
      const errorMessage = 'Network error occurred while syncing to Zotero'
      setError(errorMessage)
      onSyncError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const truncatedTitle = paperTitle.length > 60 
    ? `${paperTitle.substring(0, 60)}...` 
    : paperTitle

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Choose Zotero Destination
          </DialogTitle>
          <DialogDescription>
            Automatic sync failed for "{truncatedTitle}". Please select a specific Zotero library to sync to.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <AlertDescription>
              The paper couldn't be synced to your default Zotero library or collection-specific destinations. 
              This might be due to permission restrictions or library access issues.
            </AlertDescription>
          </Alert>

          <ZoteroDestinationSelector
            value={selectedDestination}
            onChange={setSelectedDestination}
            disabled={isLoading}
            showLabel={true}
            className="space-y-2"
          />

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSync}
            disabled={isLoading || !selectedDestination.libraryType}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sync to Selected Library
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
