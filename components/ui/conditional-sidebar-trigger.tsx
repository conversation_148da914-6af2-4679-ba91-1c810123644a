'use client'

import { SidebarTrigger } from '@/components/ui/sidebar'
import { useSidebar } from '@/components/ui/sidebar'

export function ConditionalSidebarTrigger() {
  try {
    // Try to use the sidebar context
    const sidebar = useSidebar()
    // If we get here, the context is available
    return <SidebarTrigger />
  } catch (error) {
    // If the context is not available, don't render anything
    return null
  }
}
