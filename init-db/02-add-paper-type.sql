-- Migration: Add paper_type column to papers table
-- This migration adds the missing paper_type column that was referenced in the code but missing from the schema

-- Add paper_type column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'papers' AND column_name = 'paper_type'
    ) THEN
        ALTER TABLE papers ADD COLUMN paper_type VARCHAR(50) DEFAULT 'journalArticle';
        
        -- Update existing papers to have a default paper type
        UPDATE papers SET paper_type = 'journalArticle' WHERE paper_type IS NULL;
        
        -- Add a comment for documentation
        COMMENT ON COLUMN papers.paper_type IS 'Type of paper (journalArticle, conferencePaper, book, etc.) - Zotero compatible item types';
    END IF;
END $$;
