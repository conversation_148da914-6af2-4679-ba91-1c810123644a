name: Auto Release

# Disabled: We now manage releases manually to match PaperNugget changelog versioning
# on:
#   push:
#     branches:
#       - main

on:
  workflow_dispatch:  # Allow manual triggering only

jobs:
  release:
    name: Create GitHub Release
    runs-on: ubuntu-latest

    permissions:
      contents: write  # Needed to push tags and create releases

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Get latest tag
        id: get_tag
        run: |
          git fetch --tags
          latest_tag=$(git tag --sort=-v:refname | head -n 1)
          echo "Latest tag: $latest_tag"
          echo "tag=$latest_tag" >> $GITHUB_OUTPUT

      - name: Bump version (patch)
        id: bump
        run: |
          current=${{ steps.get_tag.outputs.tag }}
          if [[ "$current" =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+)$ ]]; then
            major="${BASH_REMATCH[1]}"
            minor="${BASH_REMATCH[2]}"
            patch="${BASH_REMATCH[3]}"
            new_tag="v$major.$minor.$((patch + 1))"
          else
            new_tag="v1.0.0"
          fi
          echo "New tag: $new_tag"
          echo "new_tag=$new_tag" >> $GITHUB_OUTPUT

      - name: Create Git tag
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag ${{ steps.bump.outputs.new_tag }}
          git push origin ${{ steps.bump.outputs.new_tag }}

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ steps.bump.outputs.new_tag }}
          name: Release ${{ steps.bump.outputs.new_tag }}
          generate_release_notes: true
