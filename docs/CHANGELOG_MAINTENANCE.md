# Changelog Maintenance Guide

This guide explains how to maintain the PaperNugget changelog.

## Overview

The changelog is implemented as a static page (`/changelog`) that displays version history, features, bug fixes, and improvements. It's accessible through the sidebar navigation and shows a version badge for easy discovery.

## File Structure

```
lib/changelog-data.ts          # Changelog data and helper functions
app/changelog/page.tsx         # Changelog page component
components/app-sidebar.tsx     # Sidebar with changelog menu item
```

## Adding New Entries

### 1. Edit the Changelog Data

Open `lib/changelog-data.ts` and add a new entry at the **beginning** of the `changelog` array:

```typescript
{
  version: '1.3.0',           // Semantic version
  date: '2025-08-17',         // Release date (YYYY-MM-DD)
  type: 'minor',              // 'major' | 'minor' | 'patch'
  changes: [
    {
      type: 'feature',        // Change type (see below)
      title: 'New Feature',   // Brief title
      description: 'Detailed description of the feature or change.'
    }
    // ... more changes
  ]
}
```

### 2. Change Types

Use these change types for proper categorization and icons:

- **`feature`** - New features and functionality (green, sparkles icon)
- **`bugfix`** - Bug fixes and corrections (red, bug icon)
- **`improvement`** - Enhancements to existing features (blue, zap icon)
- **`security`** - Security-related changes (purple, shield icon)
- **`breaking`** - Breaking changes (orange, wrench icon)

### 3. Version Types

- **`major`** - Breaking changes, major new features (red badge)
- **`minor`** - New features, significant improvements (blue badge)
- **`patch`** - Bug fixes, small improvements (gray badge)

## Best Practices

### Writing Good Changelog Entries

1. **Be User-Focused**: Write from the user's perspective
   - ✅ "Fixed issue where review progress was not saved"
   - ❌ "Fixed NaN calculation in spaced repetition algorithm"

2. **Be Specific**: Include enough detail to understand the impact
   - ✅ "Added feedback submission system in account settings"
   - ❌ "Added feedback system"

3. **Group Related Changes**: Combine related fixes/features when appropriate

4. **Use Action Words**: Start with verbs like "Added", "Fixed", "Improved"

### Version Numbering

Follow [Semantic Versioning](https://semver.org/):

- **Major (X.0.0)**: Breaking changes, major rewrites
- **Minor (X.Y.0)**: New features, significant improvements
- **Patch (X.Y.Z)**: Bug fixes, small improvements

### Release Timing

Add changelog entries:
- **Before release**: When preparing a new version
- **After testing**: Once features are confirmed working
- **In batches**: Group multiple changes into logical releases

## Example Entry

```typescript
{
  version: '1.3.0',
  date: '2025-08-17',
  type: 'minor',
  changes: [
    {
      type: 'feature',
      title: 'Advanced Search Filters',
      description: 'Added ability to filter papers by author, publication year, and tags with advanced search operators.'
    },
    {
      type: 'improvement',
      title: 'Faster Paper Loading',
      description: 'Optimized database queries to reduce paper list loading time by 60%.'
    },
    {
      type: 'bugfix',
      title: 'Collection Sync Issue',
      description: 'Fixed issue where collections would not sync properly with Zotero group libraries.'
    },
    {
      type: 'security',
      title: 'Enhanced API Security',
      description: 'Strengthened API authentication and added rate limiting for better security.'
    }
  ]
}
```

## Helper Functions

The changelog data file includes helper functions:

```typescript
getLatestVersion()              // Returns latest version string
getChangesByType(type)          // Filter changes by type
getRecentChanges(versions)      // Get last N versions
```

## UI Features

- **Sidebar Badge**: Shows current version (automatically updates)
- **Visual Icons**: Different icons for each change type
- **Color Coding**: Consistent color scheme for change types
- **Responsive Design**: Works on mobile and desktop
- **Feedback Link**: Links to Canny for user feedback

## Maintenance Checklist

When adding a new release:

- [ ] Add entry to `lib/changelog-data.ts` at the top of the array
- [ ] Use appropriate version number (semantic versioning)
- [ ] Include all significant changes since last release
- [ ] Use proper change types and clear descriptions
- [ ] Test the changelog page displays correctly
- [ ] Verify sidebar badge shows new version
- [ ] Deploy and announce the release

## Notes

- The changelog is static (no database) for simplicity and performance
- Version badge in sidebar automatically updates when data changes
- Entries are sorted by date (newest first)
- The page is accessible to all users (no authentication required)
