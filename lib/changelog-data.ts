export interface ChangelogEntry {
  version: string
  date: string
  type: 'major' | 'minor' | 'patch'
  changes: {
    type: 'feature' | 'bugfix' | 'improvement' | 'security' | 'breaking'
    title: string
    description: string
  }[]
}

export const changelog: ChangelogEntry[] = [
  {
    version: '1.2.4',
    date: '2025-08-16',
    type: 'patch',
    changes: [
      {
        type: 'bugfix',
        title: 'Fixed Enrichment Metrics Persistence',
        description: 'Enriched metrics (citation count, reference count) now persist permanently after enrichment, even for papers without DOI.'
      },
      {
        type: 'bugfix',
        title: 'Fixed Zotero Sync for Papers Without DOI',
        description: 'Resolved 400 bad request errors when syncing papers without DOI to custom Zotero groups by improving library ID validation.'
      },
      {
        type: 'improvement',
        title: 'Enhanced Tags Field',
        description: 'Added placeholder text to clarify that tags should be comma-separated (e.g., "machine learning, AI, neural networks").'
      },
      {
        type: 'improvement',
        title: 'Simplified Paper View Interface',
        description: 'Removed unnecessary publication fields (Journal, Volume, Issue, Pages, Publication Date) from paper view, keeping only essential DOI and URL fields.'
      }
    ]
  },
  {
    version: '1.2.3',
    date: '2025-08-16',
    type: 'patch',
    changes: [
      {
        type: 'improvement',
        title: 'Collapsible Changelog',
        description: 'Made changelog sections collapsible for better navigation. Most recent version is expanded by default, with Expand All/Collapse All controls for bulk operations.'
      }
    ]
  },
  {
    version: '1.2.2',
    date: '2025-08-16',
    type: 'patch',
    changes: [
      {
        type: 'bugfix',
        title: 'Settings Page Critical Fix',
        description: 'Fixed missing Badge component import that prevented the settings page from rendering, causing a complete page failure.'
      }
    ]
  },
  {
    version: '1.2.1',
    date: '2025-08-16',
    type: 'patch',
    changes: [
      {
        type: 'improvement',
        title: 'Privacy Settings UI Enhancement',
        description: 'Added "Coming Soon" indicators for Data Collection and Email Notifications features that are not yet implemented.'
      },
      {
        type: 'bugfix',
        title: 'UserMenu Navigation Links',
        description: 'Fixed Profile and Settings menu items in user dropdown to properly navigate to their respective pages.'
      }
    ]
  },
  {
    version: '1.2.0',
    date: '2025-08-16',
    type: 'minor',
    changes: [
      {
        type: 'feature',
        title: 'Canny Feedback Integration',
        description: 'Added feedback submission system in account settings to collect user issues and feature requests through Canny.'
      },
      {
        type: 'improvement',
        title: 'Review Status UI Enhancement',
        description: 'Improved collection review status display to clearly distinguish between "in review system" and "needs attention now" states.'
      },
      {
        type: 'bugfix',
        title: 'Review Session Progress Saving',
        description: 'Fixed critical issue where review session progress was not being saved due to invalid date calculations in spaced repetition algorithm.'
      },
      {
        type: 'bugfix',
        title: 'Review Status Calculation',
        description: 'Fixed discrepancy in review status tooltip where numbers did not add up correctly due to duplicate logic in categorization.'
      }
    ]
  },
  {
    version: '1.1.0',
    date: '2025-08-15',
    type: 'minor',
    changes: [
      {
        type: 'feature',
        title: 'Enhanced Spaced Repetition',
        description: 'Improved spaced repetition algorithm with better interval calculations and error handling for edge cases.'
      },
      {
        type: 'improvement',
        title: 'Collection Management',
        description: 'Enhanced collection cards with better visual hierarchy and status indicators for review progress.'
      },
      {
        type: 'security',
        title: 'Authentication Improvements',
        description: 'Strengthened JWT token validation and added better session management.'
      }
    ]
  },
  {
    version: '1.0.0',
    date: '2025-08-01',
    type: 'major',
    changes: [
      {
        type: 'feature',
        title: 'Initial Release',
        description: 'First stable release of PaperNugget with core paper management, collections, and spaced repetition review system.'
      },
      {
        type: 'feature',
        title: 'Zotero Integration',
        description: 'Full integration with Zotero for syncing papers and notes to personal and group libraries.'
      },
      {
        type: 'feature',
        title: 'User Management',
        description: 'Complete user authentication system with email verification, password reset, and account management.'
      },
      {
        type: 'feature',
        title: 'Admin Dashboard',
        description: 'Administrative interface for user management, system metrics, and audit logging.'
      }
    ]
  }
]

// Helper function to get the latest version
export const getLatestVersion = (): string => {
  return changelog[0]?.version || '1.0.0'
}

// Helper function to get changes by type
export const getChangesByType = (type: 'feature' | 'bugfix' | 'improvement' | 'security' | 'breaking') => {
  return changelog.flatMap(entry => 
    entry.changes.filter(change => change.type === type)
  )
}

// Helper function to get recent changes (last N versions)
export const getRecentChanges = (versions: number = 3): ChangelogEntry[] => {
  return changelog.slice(0, versions)
}

// Helper function to check if a version is recent (within last 30 days)
export const isRecentRelease = (date: string): boolean => {
  const releaseDate = new Date(date)
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  return releaseDate >= thirtyDaysAgo
}

// Helper function to get the number of recent releases
export const getRecentReleaseCount = (): number => {
  return changelog.filter(entry => isRecentRelease(entry.date)).length
}
