import { ZoteroClient } from './zotero-client'
import { papers, notes, collections } from './database'
import { Paper, Note, ZoteroSyncResult, ZoteroSettings, Collection } from './types'

export class ZoteroSyncService {
  private client: ZoteroClient
  private settings: ZoteroSettings

  constructor(apiKey: string, settings: ZoteroSettings) {
    this.client = new ZoteroClient(apiKey)
    this.settings = settings
  }

  /**
   * Determines the appropriate Zotero destination for a paper based on its collections.
   * If a paper belongs to multiple collections with different destinations, uses the first one found.
   * Falls back to global user settings if no collection-specific destination is configured.
   */
  private async getZoteroDestinationForPaper(paperId: string, userId: string): Promise<ZoteroSettings> {
    try {
      // Get all collections for the user
      const userCollections = await collections.getByUserId(userId)

      // Find collections that contain this paper and have Zotero destination configured
      const collectionsWithPaper = userCollections.filter(collection =>
        collection.paperIds.includes(paperId) &&
        collection.zoteroLibraryType &&
        // For group libraries, ensure libraryId is also set
        (collection.zoteroLibraryType === 'user' || collection.zoteroLibraryId)
      )

      // If we found a collection with specific Zotero destination, use it
      if (collectionsWithPaper.length > 0) {
        const targetCollection = collectionsWithPaper[0] // Use first match if multiple

        console.log('Using collection-specific Zotero destination:', {
          collectionId: targetCollection.id,
          collectionName: targetCollection.name,
          libraryType: targetCollection.zoteroLibraryType,
          libraryId: targetCollection.zoteroLibraryId
        })

        const collectionSettings = {
          ...this.settings,
          libraryType: targetCollection.zoteroLibraryType!,
          libraryId: targetCollection.zoteroLibraryType === 'user' ? undefined : targetCollection.zoteroLibraryId!
        }

        // Validate the collection settings
        if (collectionSettings.libraryType === 'group' && (!collectionSettings.libraryId || collectionSettings.libraryId.trim() === '')) {
          console.error('Invalid collection Zotero settings: group library type but no library ID', {
            collectionId: targetCollection.id,
            collectionName: targetCollection.name
          })
          // Fall back to global settings instead of using invalid collection settings
          console.log('Falling back to global Zotero settings due to invalid collection configuration')
          return this.settings
        }

        return collectionSettings
      }

      // Fall back to global settings
      console.log('No collection-specific Zotero destination found, using global settings:', {
        paperId,
        globalLibraryType: this.settings.libraryType,
        globalLibraryId: this.settings.libraryId,
        collectionsChecked: userCollections.length,
        collectionsWithPaper: collectionsWithPaper.length
      })
      return this.settings
    } catch (error) {
      console.error('Error determining Zotero destination for paper:', error)
      // Fall back to global settings on error
      return this.settings
    }
  }

  /**
   * Sync a paper to Zotero with fallback strategy
   */
  async syncPaper(paperId: string): Promise<ZoteroSyncResult> {
    console.log('NEW SYNC PAPER METHOD CALLED WITH FALLBACK STRATEGY', { paperId })
    try {
      // Get paper and note data
      const paper = await papers.getById(paperId)
      if (!paper) {
        return {
          success: false,
          error: 'Paper not found'
        }
      }

      const note = await notes.getByPaperId(paperId)
      if (!note) {
        return {
          success: false,
          error: 'Note not found for paper'
        }
      }

      // Update sync status to pending
      await papers.update(paperId, {
        zoteroSyncStatus: 'pending'
      })

      // Try sync with fallback strategy
      console.log('Calling syncPaperWithFallback for paper:', paperId)
      return await this.syncPaperWithFallback(paperId, paper, note)
    } catch (error) {
      console.error('Error syncing paper to Zotero:', {
        paperId,
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined
      })

      // Update sync status to error
      await papers.update(paperId, {
        zoteroSyncStatus: 'error'
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Sync paper with fallback strategy:
   * 1. Try default library configured in settings
   * 2. If permission error, try collection-specific Zotero groups
   * 3. If all fail, return error for user selection
   */
  private async syncPaperWithFallback(paperId: string, paper: any, note: any): Promise<ZoteroSyncResult> {
    console.log('FALLBACK STRATEGY: Starting syncPaperWithFallback for paper:', paperId)
    const destinations = await this.getZoteroDestinationsForPaper(paperId, paper.userId)

    console.log('FALLBACK STRATEGY: Attempting sync with fallback strategy:', {
      paperId,
      paperTitle: paper.title,
      destinationCount: destinations.length,
      destinations: destinations.map(d => ({ type: d.libraryType, id: d.libraryId }))
    })

    let lastError = ''

    // Try each destination in order
    for (let i = 0; i < destinations.length; i++) {
      const destination = destinations[i]

      console.log(`Trying destination ${i + 1}/${destinations.length}:`, {
        libraryType: destination.libraryType,
        libraryId: destination.libraryId,
        isDefault: i === 0
      })

      try {
        const result = await this.syncPaperToDestination(paperId, paper, note, destination)
        if (result.success) {
          console.log(`Successfully synced to destination ${i + 1}:`, {
            libraryType: destination.libraryType,
            libraryId: destination.libraryId,
            itemKey: result.itemKey
          })
          return result
        } else {
          lastError = result.error || 'Unknown error'
          console.log(`Failed to sync to destination ${i + 1}:`, {
            libraryType: destination.libraryType,
            libraryId: destination.libraryId,
            error: lastError
          })
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : String(error)
        console.log(`Exception syncing to destination ${i + 1}:`, {
          libraryType: destination.libraryType,
          libraryId: destination.libraryId,
          error: lastError
        })

        // If it's a permission error, continue to next destination
        if (lastError.includes('403') || lastError.includes('Cannot edit item')) {
          continue
        }

        // For other errors, stop trying
        break
      }
    }

    // All destinations failed
    await papers.update(paperId, {
      zoteroSyncStatus: 'error'
    })

    return {
      success: false,
      error: `Failed to sync to any available Zotero destination. Last error: ${lastError}`,
      requiresUserSelection: destinations.length === 0 || lastError.includes('403')
    }
  }

  /**
   * Get all possible Zotero destinations for a paper in priority order:
   * 1. Default library from user settings
   * 2. Collection-specific Zotero groups (if paper belongs to collections with Zotero destinations)
   */
  private async getZoteroDestinationsForPaper(paperId: string, userId: string): Promise<ZoteroSettings[]> {
    const destinations: ZoteroSettings[] = []

    // 1. Always try default library first
    destinations.push(this.settings)

    try {
      // 2. Get collection-specific destinations
      const userCollections = await collections.getByUserId(userId)
      const collectionsWithPaper = userCollections.filter(collection =>
        collection.paperIds.includes(paperId) &&
        collection.zoteroLibraryType &&
        // For group libraries, ensure libraryId is also set
        (collection.zoteroLibraryType === 'user' || collection.zoteroLibraryId)
      )

      // Add unique collection destinations (avoid duplicates)
      const addedDestinations = new Set<string>()
      addedDestinations.add(`${this.settings.libraryType}:${this.settings.libraryId || 'user'}`)

      for (const collection of collectionsWithPaper) {
        const destKey = `${collection.zoteroLibraryType}:${collection.zoteroLibraryId || 'user'}`
        if (!addedDestinations.has(destKey)) {
          destinations.push({
            ...this.settings,
            libraryType: collection.zoteroLibraryType!,
            libraryId: collection.zoteroLibraryType === 'user' ? undefined : collection.zoteroLibraryId!
          })
          addedDestinations.add(destKey)
        }
      }
    } catch (error) {
      console.error('Error getting collection destinations:', error)
    }

    return destinations
  }

  /**
   * Sync a paper to a specific Zotero destination
   */
  private async syncPaperToDestination(
    paperId: string,
    paper: any,
    note: any,
    destination: ZoteroSettings
  ): Promise<ZoteroSyncResult> {
    let itemKey = paper.zoteroItemKey
    let existingItem = null
    let existingItemVersion: number | undefined

    // Validate destination
    if (destination.libraryType === 'group' && (!destination.libraryId || destination.libraryId.trim() === '')) {
      return {
        success: false,
        error: 'Group library ID is required for group library sync but was not provided'
      }
    }

    // If we don't have an item key, try to find existing item by DOI first
    if (!itemKey && paper.doi) {
      existingItem = await this.client.findItemByDOI(
        destination.libraryType,
        destination.libraryId || '',
        paper.doi
      )
      if (existingItem) {
        itemKey = existingItem.key
        existingItemVersion = existingItem.version
        console.log(`Found existing Zotero item by DOI: ${paper.doi}`)
      }
    }

    // If no item found by DOI, try to find by title and year
    if (!itemKey && paper.title) {
      existingItem = await this.client.findItemByTitle(
        destination.libraryType,
        destination.libraryId || '',
        paper.title,
        paper.year
      )
      if (existingItem) {
        itemKey = existingItem.key
        existingItemVersion = existingItem.version
        console.log(`Found existing Zotero item by title: "${paper.title}"${paper.year ? ` (${paper.year})` : ''}`)
      }
    }

    // Create or update the Zotero item
    if (itemKey) {
      console.log(`Updating existing Zotero item with key: ${itemKey} (version: ${existingItemVersion})`)
    } else {
      console.log(`Creating new Zotero item for paper: "${paper.title}"`)
    }

    const itemResult = await this.client.createOrUpdateItem(
      destination.libraryType,
      destination.libraryId || '',
      paper,
      itemKey,
      existingItemVersion
    )

    if (!itemResult.success) {
      return itemResult
    }

    itemKey = itemResult.itemKey!

    // Create or update the note
    const noteResult = await this.client.createOrUpdateNote(
      destination.libraryType,
      destination.libraryId || '',
      itemKey,
      note,
      paper.zoteroNoteKey
    )

    if (!noteResult.success) {
      return noteResult
    }

    // Update paper with sync information
    await papers.update(paperId, {
      zoteroItemKey: itemKey,
      zoteroNoteKey: noteResult.noteKey!,
      zoteroLastSynced: new Date().toISOString(),
      zoteroSyncStatus: 'synced'
    })

    return {
      success: true,
      itemKey,
      noteKey: noteResult.noteKey,
      message: 'Paper synced successfully to Zotero'
    }
  }

  async syncMultiplePapers(paperIds: string[]): Promise<{
    successful: string[]
    failed: Array<{ paperId: string; error: string }>
    total: number
  }> {
    const successful: string[] = []
    const failed: Array<{ paperId: string; error: string }> = []

    for (const paperId of paperIds) {
      try {
        const result = await this.syncPaper(paperId)
        if (result.success) {
          successful.push(paperId)
        } else {
          failed.push({
            paperId,
            error: result.error || 'Unknown error'
          })
        }
      } catch (error) {
        failed.push({
          paperId,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return {
      successful,
      failed,
      total: paperIds.length
    }
  }

  async getChangedPapers(userId: string): Promise<Paper[]> {
    try {
      const userPapers = await papers.getByUserId(userId)
      
      return userPapers.filter(paper => {
        // Include papers that have never been synced
        if (!paper.zoteroLastSynced) {
          return true
        }
        
        // Include papers that have been updated since last sync
        const lastSynced = new Date(paper.zoteroLastSynced)
        const lastUpdated = new Date(paper.updatedAt)
        
        return lastUpdated > lastSynced
      })
    } catch (error) {
      console.error('Error getting changed papers:', error)
      return []
    }
  }

  async testConnection(): Promise<boolean> {
    return await this.client.testConnection(this.settings.libraryType, this.settings.libraryId)
  }

  async getUserLibraries() {
    return await this.client.getUserLibraries()
  }

  /**
   * Get available Zotero groups for the user
   */
  async getZoteroGroups(): Promise<Array<{
    id: string
    name: string
    description?: string
    type: string
    memberCount?: number
  }>> {
    try {
      return await this.client.getGroups()
    } catch (error) {
      console.error('Error fetching Zotero groups:', error)
      throw error
    }
  }

  /**
   * Search for a paper only in the default Zotero library (user's personal library)
   * Returns enriched metadata if found, null otherwise
   */
  async searchPaperInDefaultLibrary(title: string, doi?: string, authors?: string[]): Promise<{
    metadata: any
    source: string
    libraryType: 'user' | 'group'
    libraryId: string
    libraryName: string
  } | null> {
    try {
      console.log('ZoteroSyncService: Starting paper search in default library', {
        title,
        doi,
        authors,
        settingsLibraryType: this.settings.libraryType,
        settingsLibraryId: this.settings.libraryId,
        hasApiKey: !!this.settings.apiKey,
        apiKeyLength: this.settings.apiKey?.length
      })

      // Search only in default library
      const result = await this.client.searchPaperInDefaultLibrary(title, doi, authors)

      if (!result) {
        console.log('ZoteroSyncService: Paper not found in default library', {
          title,
          doi,
          authors
        })
        return null
      }

      console.log('ZoteroSyncService: Paper found in default library', {
        title,
        libraryType: result.libraryType,
        libraryId: result.libraryId,
        libraryName: result.libraryName,
        itemKey: result.item.key,
        itemTitle: result.item.title
      })

      // Convert Zotero item to our metadata format
      const metadata = this.client.convertZoteroItemToPaperMetadata(result.item)

      console.log('ZoteroSyncService: Metadata conversion completed', {
        title,
        metadataFields: Object.keys(metadata),
        hasTitle: !!metadata.title,
        hasDoi: !!metadata.doi,
        hasAbstract: !!metadata.abstract
      })

      return {
        metadata,
        source: `Zotero - ${result.libraryName}`,
        libraryType: result.libraryType,
        libraryId: result.libraryId,
        libraryName: result.libraryName
      }
    } catch (error: any) {
      console.error('ZoteroSyncService: Error searching paper in default library', {
        error: error.message,
        errorName: error.name,
        errorStack: error.stack,
        title,
        doi,
        authors,
        settingsLibraryType: this.settings.libraryType,
        settingsLibraryId: this.settings.libraryId,
        hasApiKey: !!this.settings.apiKey
      })
      return null
    }
  }

  /**
   * Search for a paper in all accessible Zotero libraries
   * Returns enriched metadata if found, null otherwise
   */
  async searchPaperInZotero(title: string, doi?: string, authors?: string[]): Promise<{
    metadata: any
    source: string
    libraryType: 'user' | 'group'
    libraryId: string
    libraryName: string
  } | null> {
    try {
      console.log('ZoteroSyncService: Starting paper search', {
        title,
        doi,
        authors,
        settingsLibraryType: this.settings.libraryType,
        settingsLibraryId: this.settings.libraryId,
        hasApiKey: !!this.settings.apiKey,
        apiKeyLength: this.settings.apiKey?.length
      })

      const result = await this.client.searchPaperInAllLibraries(title, doi, authors)

      if (!result) {
        console.log('ZoteroSyncService: Paper not found in any Zotero library', {
          title,
          doi,
          searchedLibraryType: this.settings.libraryType,
          searchedLibraryId: this.settings.libraryId
        })
        return null
      }

      console.log('ZoteroSyncService: Paper found in Zotero', {
        title,
        doi,
        foundInLibrary: result.libraryName,
        foundInLibraryType: result.libraryType,
        foundInLibraryId: result.libraryId,
        itemKey: result.item.key,
        itemTitle: result.item.title
      })

      // Convert Zotero item to our metadata format
      const metadata = this.client.convertZoteroItemToPaperMetadata(result.item)

      console.log('ZoteroSyncService: Metadata conversion completed', {
        title,
        metadataFields: Object.keys(metadata),
        hasTitle: !!metadata.title,
        hasDoi: !!metadata.doi,
        hasAbstract: !!metadata.abstract
      })

      return {
        metadata,
        source: `Zotero - ${result.libraryName}`,
        libraryType: result.libraryType,
        libraryId: result.libraryId,
        libraryName: result.libraryName
      }
    } catch (error: any) {
      console.error('ZoteroSyncService: Error searching paper in Zotero', {
        error: error.message,
        errorName: error.name,
        errorStack: error.stack,
        title,
        doi,
        authors,
        settingsLibraryType: this.settings.libraryType,
        settingsLibraryId: this.settings.libraryId,
        hasApiKey: !!this.settings.apiKey
      })
      return null
    }
  }
}

export async function createZoteroSyncService(
  apiKey: string,
  settings: ZoteroSettings
): Promise<ZoteroSyncService> {
  return new ZoteroSyncService(apiKey, settings)
}

// Helper function to get Zotero settings from user preferences
export function getZoteroSettingsFromPreferences(preferences: Record<string, any>): ZoteroSettings {
  const zotero = preferences.zotero || {}
  
  return {
    apiKey: zotero.apiKey,
    libraryType: zotero.libraryType || 'user',
    libraryId: zotero.libraryId,
    enabled: zotero.enabled || false
  }
}

// Helper function to validate Zotero settings
export function validateZoteroSettings(settings: ZoteroSettings): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!settings.apiKey) {
    errors.push('API key is required')
  }

  if (settings.libraryType === 'group' && (!settings.libraryId || settings.libraryId.trim() === '')) {
    errors.push('Library ID is required for group libraries')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}
