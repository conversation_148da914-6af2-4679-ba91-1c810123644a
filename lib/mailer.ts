/**
 * Email service using <PERSON><PERSON><PERSON><PERSON>
 * Handles sending verification emails and other transactional emails
 */

// Dynamic import for nodemail<PERSON> to avoid bundling issues
import { config } from './config'

interface EmailOptions {
  to: string
  subject: string
  html: string
  text: string
}

class MailerError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message)
    this.name = 'MailerError'
  }
}

// Create transporter instance
let transporter: any | null = null

async function createTransporter(): Promise<any> {
  if (transporter) {
    return transporter
  }

  // Dynamic import to avoid bundling issues
  const nodemailer = await import('nodemailer')

  const transportConfig: any = {
    host: config.email.smtpHost,
    port: config.email.smtpPort,
    // Gmail uses port 587 with STARTTLS, port 465 with SSL
    secure: config.email.smtpPort === 465, // true for 465 (SSL), false for 587 (STARTTLS)
    auth: config.email.smtpUser && config.email.smtpPass ? {
      user: config.email.smtpUser,
      pass: config.email.smtpPass,
    } : undefined,
    tls: {
      // For Gmail and other production SMTP servers
      rejectUnauthorized: config.nodeEnv === 'production'
    }
  }

  // Access nodemailer's createTransport across CJS/ESM interop
  const createTransport = (nodemailer as any).createTransport || (nodemailer as any).default?.createTransport

  if (typeof createTransport !== 'function') {
    throw new MailerError('Nodemailer createTransport not available. Check nodemailer installation/import.')
  }

  transporter = createTransport(transportConfig)

  // Verify connection configuration in development
  if (config.nodeEnv === 'development') {
    transporter.verify((error, success) => {
      if (error) {
        console.warn('⚠️  SMTP connection verification failed:', error.message)
        console.warn('   Email sending may not work properly')
      } else {
        console.log('✅ SMTP server is ready to take our messages')
      }
    })
  }

  return transporter
}

async function sendEmail(options: EmailOptions): Promise<void> {
  try {
    const transport = await createTransporter()
    
    const mailOptions = {
      from: config.email.emailFrom,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    }

    const info = await transport.sendMail(mailOptions)
    
    if (config.nodeEnv === 'development') {
      console.log('📧 Email sent:', {
        messageId: info.messageId,
        to: options.to,
        subject: options.subject,
      })
    }
  } catch (error) {
    console.error('❌ Failed to send email:', error)
    throw new MailerError(
      'Failed to send email',
      error instanceof Error ? error : new Error(String(error))
    )
  }
}

function generateVerificationEmailTemplate(verificationUrl: string, userEmail: string): { html: string; text: string } {
  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email - PaperNugget</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
        .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
        .button:hover { background: #1d4ed8; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #6b7280; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">📄 PaperNugget</div>
        </div>
        
        <div class="content">
          <h2>Verify Your Email Address</h2>
          <p>Hello!</p>
          <p>Thank you for signing up for PaperNugget. To complete your registration and start organizing your research papers, please verify your email address by clicking the button below:</p>
          
          <p style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
          </p>
          
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f1f5f9; padding: 10px; border-radius: 4px; font-family: monospace;">
            ${verificationUrl}
          </p>
        </div>
        
        <div class="warning">
          <strong>⏰ Important:</strong> This verification link will expire in 24 hours for security reasons.
        </div>
        
        <div class="footer">
          <p>If you didn't create an account with PaperNugget, you can safely ignore this email.</p>
          <p>Need help? Contact our support team.</p>
          <p>&copy; 2024 PaperNugget. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
PaperNugget - Verify Your Email Address

Hello!

Thank you for signing up for PaperNugget. To complete your registration and start organizing your research papers, please verify your email address by visiting this link:

${verificationUrl}

IMPORTANT: This verification link will expire in 24 hours for security reasons.

If you didn't create an account with PaperNugget, you can safely ignore this email.

Need help? Contact our support team.

© 2024 PaperNugget. All rights reserved.
  `

  return { html, text }
}

export async function sendVerificationEmail(to: string, token: string, baseUrl?: string): Promise<void> {
  // Use provided baseUrl or fallback to config for backwards compatibility
  const appUrl = baseUrl || config.email.appUrl
  const verificationUrl = `${appUrl}/api/auth/verify-email?token=${encodeURIComponent(token)}`
  const { html, text } = generateVerificationEmailTemplate(verificationUrl, to)

  await sendEmail({
    to,
    subject: 'Verify your email address - PaperNugget',
    html,
    text,
  })
}

function generatePasswordResetEmailTemplate(resetUrl: string, userEmail: string): { html: string; text: string } {
  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password - PaperNugget</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
        .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 6px; font-weight: 500; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .footer { text-align: center; color: #6b7280; font-size: 14px; margin-top: 30px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">📄 PaperNugget</div>
        </div>

        <div class="content">
          <h2>Reset Your Password</h2>
          <p>Hello!</p>
          <p>We received a request to reset the password for your PaperNugget account. If you made this request, click the button below to reset your password:</p>

          <p style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" class="button">Reset Password</a>
          </p>

          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f1f5f9; padding: 10px; border-radius: 4px; font-family: monospace;">
            ${resetUrl}
          </p>
        </div>

        <div class="warning">
          <strong>⏰ Important:</strong> This password reset link will expire in 1 hour for security reasons.
        </div>

        <div class="warning">
          <strong>🔒 Security Notice:</strong> If you didn't request a password reset, you can safely ignore this email. Your account remains secure.
        </div>

        <div class="footer">
          <p>Need help? Contact our support team.</p>
          <p>© 2024 PaperNugget. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
PaperNugget - Reset Your Password

Hello!

We received a request to reset the password for your PaperNugget account. If you made this request, visit this link to reset your password:

${resetUrl}

IMPORTANT: This password reset link will expire in 1 hour for security reasons.

SECURITY NOTICE: If you didn't request a password reset, you can safely ignore this email. Your account remains secure.

Need help? Contact our support team.

© 2024 PaperNugget. All rights reserved.
  `

  return { html, text }
}

export async function sendPasswordResetEmail(to: string, token: string, baseUrl?: string): Promise<void> {
  // Use provided baseUrl or fallback to config for backwards compatibility
  const appUrl = baseUrl || config.email.appUrl
  const resetUrl = `${appUrl}/auth/reset-password?token=${encodeURIComponent(token)}`
  const { html, text } = generatePasswordResetEmailTemplate(resetUrl, to)

  await sendEmail({
    to,
    subject: 'Reset your password - PaperNugget',
    html,
    text,
  })
}

interface DeletionResult {
  type: string
  status: 'in_progress' | 'done' | 'failed'
  count?: number
  error?: string
}

function generateAccountDeletionEmailTemplate(userEmail: string, deletionResults: DeletionResult[]): { html: string; text: string } {
  const formatStatus = (status: string) => {
    switch (status) {
      case 'done': return '✅ Completed'
      case 'failed': return '❌ Failed'
      case 'in_progress': return '⏳ In Progress'
      default: return status
    }
  }

  const formatResultRow = (result: DeletionResult) => {
    const countText = result.count !== undefined ? ` (${result.count} items)` : ''
    const errorText = result.error ? ` - Error: ${result.error}` : ''
    return `${result.type}${countText}: ${formatStatus(result.status)}${errorText}`
  }

  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Account Deletion Confirmation - PaperNugget</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
        .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .deletion-results { background: #fff; border: 1px solid #e2e8f0; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .result-item { padding: 8px 0; border-bottom: 1px solid #f1f5f9; font-family: monospace; font-size: 14px; }
        .result-item:last-child { border-bottom: none; }
        .status-done { color: #059669; }
        .status-failed { color: #dc2626; }
        .status-progress { color: #d97706; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 15px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #64748b; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">PaperNugget</div>
        </div>

        <div class="content">
          <h2>Account Deletion Confirmation</h2>
          <p>Hello,</p>
          <p>Your PaperNugget account (<strong>${userEmail}</strong>) has been permanently deleted as requested. Below is a summary of the data deletion process:</p>

          <div class="deletion-results">
            <h3>Deletion Summary</h3>
            ${deletionResults.map(result => `
              <div class="result-item status-${result.status.replace('_', '-')}">
                ${formatResultRow(result)}
              </div>
            `).join('')}
          </div>
        </div>

        <div class="warning">
          <strong>⚠️ Important:</strong> This action is permanent and cannot be undone. All your data has been removed from our systems.
        </div>

        <div class="footer">
          <p>You can register again at any time with the same email address if you wish to use PaperNugget in the future.</p>
          <p>If you have any questions or concerns, please contact our support team.</p>
          <p>&copy; 2024 PaperNugget. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
PaperNugget - Account Deletion Confirmation

Hello,

Your PaperNugget account (${userEmail}) has been permanently deleted as requested. Below is a summary of the data deletion process:

Deletion Summary:
${deletionResults.map(formatResultRow).join('\n')}

IMPORTANT: This action is permanent and cannot be undone. All your data has been removed from our systems.

You can register again at any time with the same email address if you wish to use PaperNugget in the future.

If you have any questions or concerns, please contact our support team.

© 2024 PaperNugget. All rights reserved.
  `

  return { html, text }
}

export async function sendAccountDeletionEmail(to: string, deletionResults: DeletionResult[]): Promise<void> {
  const { html, text } = generateAccountDeletionEmailTemplate(to, deletionResults)

  await sendEmail({
    to,
    subject: 'Account deletion confirmation - PaperNugget',
    html,
    text,
  })
}

export type { DeletionResult }

// Health check function for SMTP connectivity
export async function checkSMTPHealth(): Promise<{ healthy: boolean; error?: string }> {
  try {
    const transport = await createTransporter()
    await transport.verify()
    return { healthy: true }
  } catch (error) {
    return { 
      healthy: false, 
      error: error instanceof Error ? error.message : 'Unknown SMTP error'
    }
  }
}

export { MailerError }
