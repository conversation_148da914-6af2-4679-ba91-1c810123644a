import { ZoteroItem, ZoteroNote, ZoteroSyncResult, ZoteroLibrary, Paper, Note } from './types'

// Zotero item type field mappings
const ZOTERO_FIELD_MAPPINGS: Record<string, {
  venueField?: string
  supportedFields: string[]
}> = {
  journalArticle: {
    venueField: 'publicationTitle',
    supportedFields: ['title', 'creators', 'abstractNote', 'publicationTitle', 'volume', 'issue', 'pages', 'date', 'DOI', 'url', 'tags']
  },
  conferencePaper: {
    venueField: 'proceedingsTitle',
    supportedFields: ['title', 'creators', 'abstractNote', 'proceedingsTitle', 'volume', 'pages', 'date', 'DOI', 'url', 'tags']
  },
  book: {
    venueField: 'publisher',
    supportedFields: ['title', 'creators', 'abstractNote', 'publisher', 'place', 'date', 'ISBN', 'url', 'tags']
  },
  bookSection: {
    venueField: 'bookTitle',
    supportedFields: ['title', 'creators', 'abstractNote', 'bookTitle', 'publisher', 'pages', 'date', 'ISBN', 'url', 'tags']
  },
  blogPost: {
    venueField: 'blogTitle',
    supportedFields: ['title', 'creators', 'abstractNote', 'blogTitle', 'date', 'url', 'tags']
  },
  webpage: {
    venueField: 'websiteTitle',
    supportedFields: ['title', 'creators', 'abstractNote', 'websiteTitle', 'date', 'url', 'tags']
  },
  thesis: {
    venueField: 'university',
    supportedFields: ['title', 'creators', 'abstractNote', 'university', 'thesisType', 'date', 'url', 'tags']
  },
  report: {
    venueField: 'institution',
    supportedFields: ['title', 'creators', 'abstractNote', 'institution', 'reportType', 'reportNumber', 'date', 'url', 'tags']
  },
  preprint: {
    venueField: 'repository',
    supportedFields: ['title', 'creators', 'abstractNote', 'repository', 'date', 'DOI', 'url', 'tags']
  },
  manuscript: {
    supportedFields: ['title', 'creators', 'abstractNote', 'date', 'url', 'tags']
  },
  presentation: {
    venueField: 'meetingName',
    supportedFields: ['title', 'creators', 'abstractNote', 'meetingName', 'place', 'date', 'url', 'tags']
  },
  patent: {
    supportedFields: ['title', 'creators', 'abstractNote', 'country', 'patentNumber', 'date', 'url', 'tags']
  },
  dataset: {
    venueField: 'repository',
    supportedFields: ['title', 'creators', 'abstractNote', 'repository', 'date', 'DOI', 'url', 'tags']
  },
  software: {
    venueField: 'repository',
    supportedFields: ['title', 'creators', 'abstractNote', 'repository', 'versionNumber', 'date', 'url', 'tags']
  },
  magazineArticle: {
    venueField: 'publicationTitle',
    supportedFields: ['title', 'creators', 'abstractNote', 'publicationTitle', 'volume', 'issue', 'pages', 'date', 'url', 'tags']
  },
  newspaperArticle: {
    venueField: 'publicationTitle',
    supportedFields: ['title', 'creators', 'abstractNote', 'publicationTitle', 'pages', 'date', 'url', 'tags']
  },
  forumPost: {
    venueField: 'forumTitle',
    supportedFields: ['title', 'creators', 'abstractNote', 'forumTitle', 'date', 'url', 'tags']
  },
  email: {
    supportedFields: ['title', 'creators', 'abstractNote', 'date', 'url', 'tags']
  },
  letter: {
    supportedFields: ['title', 'creators', 'abstractNote', 'date', 'url', 'tags']
  },
  interview: {
    venueField: 'medium',
    supportedFields: ['title', 'creators', 'abstractNote', 'medium', 'date', 'url', 'tags']
  },
  document: {
    supportedFields: ['title', 'creators', 'abstractNote', 'date', 'url', 'tags']
  }
}

export class ZoteroClient {
  private apiKey: string
  private baseUrl = 'https://api.zotero.org'
  private rateLimitDelay = 1000 // 1 second between requests to respect rate limits
  private userIdCache: string | null = null

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  /**
   * Get the user ID associated with the API key, with caching
   */
  private async getUserId(): Promise<string> {
    if (this.userIdCache) {
      return this.userIdCache
    }

    try {
      const response = await this.makeRequest('GET', '/keys/current')
      if (!response.ok) {
        throw new Error(`Failed to get API key information: ${response.status}`)
      }

      const keyData = await response.json()
      if (!keyData.userID) {
        throw new Error('Could not get user ID from API key')
      }

      this.userIdCache = keyData.userID
      return this.userIdCache
    } catch (error) {
      console.error('Error getting user ID:', error)
      throw error
    }
  }

  private async makeRequest(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    headers: Record<string, string> = {}
  ): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`

    const requestHeaders = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'Zotero-API-Version': '3',
      ...headers
    }

    const config: RequestInit = {
      method,
      headers: requestHeaders,
    }

    if (data && (method === 'POST' || method === 'PUT')) {
      config.body = JSON.stringify(data)
    }

    console.log('ZoteroClient: Making API request', {
      method,
      endpoint,
      url,
      hasData: !!data,
      hasApiKey: !!this.apiKey,
      apiKeyLength: this.apiKey?.length,
      headers: Object.keys(requestHeaders)
    })

    // Add rate limiting delay
    await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay))

    try {
      const response = await fetch(url, config)

      console.log('ZoteroClient: API response received', {
        method,
        endpoint,
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        contentType: response.headers.get('content-type')
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('ZoteroClient: API request failed', {
          method,
          endpoint,
          status: response.status,
          statusText: response.statusText,
          errorText,
          url,
          hasApiKey: !!this.apiKey,
          apiKeyLength: this.apiKey?.length
        })
        throw new Error(`Zotero API error: ${response.status} ${response.statusText} - ${errorText}`)
      }

      return response
    } catch (fetchError: any) {
      console.error('ZoteroClient: Network/fetch error during API request', {
        method,
        endpoint,
        url,
        error: fetchError.message,
        errorName: fetchError.name,
        errorStack: fetchError.stack,
        hasApiKey: !!this.apiKey
      })
      throw fetchError
    }
  }

  async getUserLibraries(): Promise<ZoteroLibrary[]> {
    try {
      // Always include the user's personal library
      const libraries: ZoteroLibrary[] = [
        {
          id: 'user',
          name: 'My Library',
          type: 'user'
        }
      ]

      // Try to fetch group libraries
      try {
        // First, get the user ID from the API key
        const keyResponse = await this.makeRequest('GET', `/keys/${this.apiKey}`)
        const keyData = await keyResponse.json()

        if (!keyData.userID) {
          console.warn('Could not get user ID from API key')
          return libraries
        }

        // Now fetch groups using the correct endpoint
        const groupsResponse = await this.makeRequest('GET', `/users/${keyData.userID}/groups`)
        const groups = await groupsResponse.json()

        if (Array.isArray(groups)) {
          groups.forEach((group: any) => {
            if (group && group.id && group.data && group.data.name) {
              libraries.push({
                id: group.id.toString(),
                name: group.data.name,
                type: 'group',
                description: group.data.description || ''
              })
            }
          })
        }
      } catch (groupError) {
        console.warn('Could not fetch group libraries (this is normal if user has no groups):', groupError)
        // Don't throw here - user might not have any groups, which is fine
      }

      return libraries
    } catch (error) {
      console.error('Error fetching user libraries:', error)
      throw new Error(`Failed to fetch libraries: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async findItemByDOI(libraryType: 'user' | 'group', libraryId: string, doi: string): Promise<ZoteroItem | null> {
    try {
      let libraryPath: string
      if (libraryType === 'user') {
        const userId = await this.getUserId()
        libraryPath = `/users/${userId}`
      } else {
        libraryPath = `/groups/${libraryId}`
      }
      const response = await this.makeRequest('GET', `${libraryPath}/items?q=${encodeURIComponent(doi)}&qmode=everything`)
      const items = await response.json()

      // Look for exact DOI match
      for (const item of items) {
        if (item.data.DOI === doi) {
          // Return the full item object including version and key
          return {
            ...item.data,
            key: item.key,
            version: item.version
          }
        }
      }

      return null
    } catch (error) {
      console.error('Error finding item by DOI:', error)
      return null
    }
  }

  async findItemByTitle(libraryType: 'user' | 'group', libraryId: string, title: string, year?: number): Promise<ZoteroItem | null> {
    try {
      let libraryPath: string
      if (libraryType === 'user') {
        const userId = await this.getUserId()
        libraryPath = `/users/${userId}`
      } else {
        libraryPath = `/groups/${libraryId}`
      }

      // Search for items using the title as query
      const response = await this.makeRequest('GET', `${libraryPath}/items?q=${encodeURIComponent(title)}&qmode=titleCreatorYear`)
      const items = await response.json()

      // Look for title matches with optional year filtering
      for (const item of items) {
        if (item.data && item.data.title) {
          const itemTitle = item.data.title.toLowerCase().trim()
          const searchTitle = title.toLowerCase().trim()

          // Check for exact title match or very close match
          if (this.isTitleMatch(itemTitle, searchTitle)) {
            // If year is provided, also check year match
            if (year) {
              const itemYear = this.extractYearFromItem(item.data)
              if (itemYear && itemYear === year) {
                // Return the full item object including version and key
                return {
                  ...item.data,
                  key: item.key,
                  version: item.version
                }
              }
            } else {
              // If no year provided, return first title match
              // Return the full item object including version and key
              return {
                ...item.data,
                key: item.key,
                version: item.version
              }
            }
          }
        }
      }

      return null
    } catch (error) {
      console.error('Error finding item by title:', error)
      return null
    }
  }

  private isTitleMatch(itemTitle: string, searchTitle: string): boolean {
    // Remove common punctuation and normalize whitespace
    const normalize = (str: string) => str
      .replace(/[.,;:!?()[\]{}""'']/g, '')
      .replace(/\s+/g, ' ')
      .trim()
      .toLowerCase()

    const normalizedItemTitle = normalize(itemTitle)
    const normalizedSearchTitle = normalize(searchTitle)

    // Check for exact match after normalization
    if (normalizedItemTitle === normalizedSearchTitle) {
      return true
    }

    // Check if one title contains the other (for cases where one might be truncated)
    if (normalizedItemTitle.includes(normalizedSearchTitle) || normalizedSearchTitle.includes(normalizedItemTitle)) {
      // Only consider it a match if the shorter title is at least 80% of the longer one
      const minLength = Math.min(normalizedItemTitle.length, normalizedSearchTitle.length)
      const maxLength = Math.max(normalizedItemTitle.length, normalizedSearchTitle.length)
      return minLength / maxLength >= 0.8
    }

    return false
  }

  private extractYearFromItem(itemData: any): number | null {
    // Try to extract year from the date field
    if (itemData.date) {
      const yearMatch = itemData.date.match(/\b(19|20)\d{2}\b/)
      if (yearMatch) {
        return parseInt(yearMatch[0], 10)
      }
    }

    // Try to extract from other date-related fields
    if (itemData.dateAdded) {
      const year = new Date(itemData.dateAdded).getFullYear()
      if (year >= 1900 && year <= new Date().getFullYear()) {
        return year
      }
    }

    return null
  }

  private buildZoteroItem(paper: Paper, itemType: string, existingData?: any): any {
    // Ensure we have a valid itemType, fallback to 'document' if not provided
    const validItemType = itemType || paper.paperType || 'document'
    const fieldMapping = ZOTERO_FIELD_MAPPINGS[validItemType] || ZOTERO_FIELD_MAPPINGS['document']
    const venue = paper.venue || paper.journal || ''

    // Start with existing data if updating, otherwise start fresh
    const item: any = existingData ? { ...existingData } : {}

    // Ensure itemType is always set (critical for Zotero API)
    item.itemType = validItemType

    console.log(`Building Zotero item for paper "${paper.title}" with itemType: ${validItemType}`)

    // Set basic fields that are supported by most item types
    if (fieldMapping.supportedFields.includes('title')) {
      item.title = paper.title
    }
    if (fieldMapping.supportedFields.includes('creators')) {
      item.creators = paper.authors.map(author => ({
        creatorType: 'author',
        name: author
      }))
    }
    if (fieldMapping.supportedFields.includes('abstractNote')) {
      item.abstractNote = paper.abstract || ''
    }
    if (fieldMapping.supportedFields.includes('date')) {
      item.date = paper.year?.toString() || paper.publicationDate || ''
    }
    if (fieldMapping.supportedFields.includes('DOI')) {
      item.DOI = paper.doi || ''
    }
    if (fieldMapping.supportedFields.includes('url')) {
      item.url = paper.url || ''
    }
    if (fieldMapping.supportedFields.includes('tags')) {
      item.tags = paper.tags.map(tag => ({ tag }))
    }

    // Set volume, issue, pages only if supported
    if (fieldMapping.supportedFields.includes('volume')) {
      item.volume = paper.volume || ''
    }
    if (fieldMapping.supportedFields.includes('issue')) {
      item.issue = paper.issue || ''
    }
    if (fieldMapping.supportedFields.includes('pages')) {
      item.pages = paper.pages || ''
    }

    // Set venue field based on item type
    if (fieldMapping.venueField && venue && fieldMapping.supportedFields.includes(fieldMapping.venueField)) {
      item[fieldMapping.venueField] = venue
    }

    // Clean up fields that shouldn't be there for this item type
    const allPossibleVenueFields = ['publicationTitle', 'proceedingsTitle', 'bookTitle', 'blogTitle', 'websiteTitle', 'university', 'institution', 'repository', 'meetingName', 'forumTitle', 'medium', 'publisher']
    allPossibleVenueFields.forEach(field => {
      if (field !== fieldMapping.venueField && !fieldMapping.supportedFields.includes(field)) {
        delete item[field]
      }
    })

    // Clean up other fields that aren't supported
    const allPossibleFields = ['volume', 'issue', 'pages', 'DOI', 'ISBN', 'patentNumber', 'reportNumber', 'versionNumber', 'thesisType', 'reportType', 'place', 'country']
    allPossibleFields.forEach(field => {
      if (!fieldMapping.supportedFields.includes(field)) {
        delete item[field]
      }
    })

    return item
  }

  async createOrUpdateItem(
    libraryType: 'user' | 'group',
    libraryId: string,
    paper: Paper,
    existingItemKey?: string,
    existingItemVersion?: number
  ): Promise<ZoteroSyncResult> {
    try {
      let libraryPath: string
      if (libraryType === 'user') {
        const userId = await this.getUserId()
        libraryPath = `/users/${userId}`
      } else {
        libraryPath = `/groups/${libraryId}`
      }

      let response: Response
      let itemKey: string

      if (existingItemKey) {
        // For updates, we need to get the current item first to preserve all fields
        const currentItemResponse = await this.makeRequest('GET', `${libraryPath}/items/${existingItemKey}`)
        const currentItem = await currentItemResponse.json()

        // Build updated item using field mapping, preserving version and other metadata
        const updatedItem = this.buildZoteroItem(paper, currentItem.data.itemType, {
          ...currentItem.data,
          version: existingItemVersion || currentItem.version
        })

        response = await this.makeRequest('PUT', `${libraryPath}/items/${existingItemKey}`, updatedItem)
        itemKey = existingItemKey
      } else {
        // Create new item using the paper's specified type and field mapping
        const itemType = paper.paperType || 'document' // Fallback to 'document' if no paperType
        console.log(`Creating new Zotero item for paper "${paper.title}" with paperType: ${paper.paperType}, using itemType: ${itemType}`)
        const zoteroItem = this.buildZoteroItem(paper, itemType)

        console.log(`Zotero item to be created:`, JSON.stringify(zoteroItem, null, 2))
        response = await this.makeRequest('POST', `${libraryPath}/items`, [zoteroItem])
        const result = await response.json()

        // Handle different response structures
        if (result.successful && Object.keys(result.successful).length > 0) {
          // Handle object-based successful responses like {"0": {"key": "ABC123"}}
          const firstKey = Object.keys(result.successful)[0]
          itemKey = result.successful[firstKey].key
        } else if (result.successful && Array.isArray(result.successful) && result.successful.length > 0) {
          // Handle array-based successful responses
          itemKey = result.successful[0].key
        } else if (result.success && Object.keys(result.success).length > 0) {
          // Handle object-based success responses like {"0": "ABC123"}
          const firstKey = Object.keys(result.success)[0]
          itemKey = result.success[firstKey]
        } else if (result.success && Array.isArray(result.success) && result.success.length > 0) {
          // Handle array-based success responses
          itemKey = result.success[0]
        } else if (result[0] && result[0].key) {
          itemKey = result[0].key
        } else {
          throw new Error(`Failed to create Zotero item: ${JSON.stringify(result)}`)
        }
      }

      return {
        success: true,
        itemKey,
        message: existingItemKey ? 'Item updated successfully' : 'Item created successfully'
      }
    } catch (error) {
      console.error('Error creating/updating Zotero item:', error)

      // Provide more specific error handling for common Zotero API issues
      let errorMessage = 'Unknown error occurred'
      if (error instanceof Error) {
        if (error.message.includes('is not a valid field for type')) {
          // Extract the field and type from the error message
          const fieldMatch = error.message.match(/'([^']+)' is not a valid field for type '([^']+)'/)
          if (fieldMatch) {
            errorMessage = `Field '${fieldMatch[1]}' is not valid for ${fieldMatch[2]} items. This has been automatically corrected.`
          } else {
            errorMessage = 'Field validation error: Some fields are not compatible with the selected item type'
          }
        } else {
          errorMessage = error.message
        }
      }

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  async createOrUpdateNote(
    libraryType: 'user' | 'group',
    libraryId: string,
    itemKey: string,
    note: Note,
    existingNoteKey?: string
  ): Promise<ZoteroSyncResult> {
    try {
      let libraryPath: string
      if (libraryType === 'user') {
        const userId = await this.getUserId()
        libraryPath = `/users/${userId}`
      } else {
        libraryPath = `/groups/${libraryId}`
      }

      // Format the note content
      const noteContent = this.formatNoteContent(note)

      const zoteroNote: ZoteroNote = {
        itemType: 'note',
        parentItem: itemKey,
        note: noteContent,
        tags: [{ tag: 'PaperNugget' }],
        relations: {},
        collections: []
      }

      // Verify parent item exists in the selected Zotero library before creating/updating the note
      try {
        const parentCheck = await this.makeRequest('GET', `${libraryPath}/items/${itemKey}`)
        if (!parentCheck.ok) {
          throw new Error(`Parent item not found in target library: ${parentCheck.status} ${parentCheck.statusText}`)
        }
      } catch (parentErr: any) {
        console.error('ZoteroClient: Parent item verification failed', {
          libraryType,
          libraryId,
          libraryPath,
          itemKey,
          error: parentErr?.message
        })
        throw parentErr
      }

      // Log full payload for diagnostics
      const payload = [{...zoteroNote}]
      const payloadSize = JSON.stringify(payload).length
      console.log('ZoteroClient: Prepared Zotero note payload', {
        libraryType,
        libraryId,
        libraryPath,
        parentItem: itemKey,
        existingNoteKey,
        payload,
        payloadSize
      })
      if (payloadSize > 50000) {
        console.warn('ZoteroClient: Note payload is large and may be rejected by Zotero', { payloadSize })
      }


      let response: Response
      let noteKey: string

      if (existingNoteKey) {
        // Update existing note - get current note first to get version and preserve existing data
        try {
          const currentNoteResponse = await this.makeRequest('GET', `${libraryPath}/items/${existingNoteKey}`)
          if (!currentNoteResponse.ok) {
            throw new Error(`Failed to fetch existing note: ${currentNoteResponse.status} ${currentNoteResponse.statusText}`)
          }

          const currentNote = await currentNoteResponse.json()

          // Build updated note preserving existing structure and adding version
          const updatedNote = {
            ...currentNote.data, // Preserve all existing fields
            note: noteContent,   // Update the note content
            tags: [{ tag: 'PaperNugget' }], // Update tags
            version: currentNote.version // Include version for conflict resolution
          }

          response = await this.makeRequest('PUT', `${libraryPath}/items/${existingNoteKey}`, updatedNote)
          noteKey = existingNoteKey
        } catch (versionError) {
          console.error('Error getting note version, trying to create new note instead:', versionError)
          // If we can't get the existing note, create a new one
          response = await this.makeRequest('POST', `${libraryPath}/items`, [zoteroNote])
          const result = await response.json()
          if (result.successful && Object.keys(result.successful).length > 0) {
            // Handle object-based successful responses like {"0": {"key": "ABC123"}}
            const firstKey = Object.keys(result.successful)[0]
            noteKey = result.successful[firstKey].key
          } else if (result.successful && Array.isArray(result.successful) && result.successful.length > 0) {
            // Handle array-based successful responses
            noteKey = result.successful[0].key
          } else if (result.success && Object.keys(result.success).length > 0) {
            // Handle object-based success responses like {"0": "ABC123"}
            const firstKey = Object.keys(result.success)[0]
            noteKey = result.success[firstKey]
          } else if (result.success && Array.isArray(result.success) && result.success.length > 0) {
            // Handle array-based success responses
            noteKey = result.success[0]
          } else if (result[0] && result[0].key) {
            noteKey = result[0].key
          } else {
            throw new Error(`Failed to create new note after version error: ${JSON.stringify(result)}`)
          }
        }

        // Verify note exists after creation
        try {
          const verifyResp = await this.makeRequest('GET', `${libraryPath}/items/${noteKey}`)
          if (!verifyResp.ok) {
            throw new Error(`Note verification failed: ${verifyResp.status} ${verifyResp.statusText}`)
          }
        } catch (verifyErr: any) {
          console.error('ZoteroClient: Created note could not be verified', {
            libraryPath,
            noteKey,
            error: verifyErr?.message
          })
          throw verifyErr
        }


	      // Post-create verification for new-note path only
	      if (!existingNoteKey) {
	        try {
	          const verifyResp = await this.makeRequest('GET', `${libraryPath}/items/${noteKey}`)
	          if (!verifyResp.ok) {
	            throw new Error(`Note verification failed: ${verifyResp.status} ${verifyResp.statusText}`)
	          }
	        } catch (verifyErr: any) {
	          console.error('ZoteroClient: Created note could not be verified (new-note path)', {
	            libraryPath,
	            noteKey,
	            error: verifyErr?.message
	          })
	          throw verifyErr
	        }
	      }

      } else {
        // Create new note
        response = await this.makeRequest('POST', `${libraryPath}/items`, [zoteroNote])
        const result = await response.json()

        // Handle different response structures
        if (result.successful && Object.keys(result.successful).length > 0) {
          // Handle object-based successful responses like {"0": {"key": "ABC123"}}
          const firstKey = Object.keys(result.successful)[0]
          noteKey = result.successful[firstKey].key
        } else if (result.successful && Array.isArray(result.successful) && result.successful.length > 0) {
          // Handle array-based successful responses
          noteKey = result.successful[0].key
        } else if (result.success && Object.keys(result.success).length > 0) {
          // Handle object-based success responses like {"0": "ABC123"}
          const firstKey = Object.keys(result.success)[0]
          noteKey = result.success[firstKey]
        } else if (result.success && Array.isArray(result.success) && result.success.length > 0) {
          // Handle array-based success responses
          noteKey = result.success[0]
        } else if (result[0] && result[0].key) {
          noteKey = result[0].key
        } else {
          throw new Error(`Failed to create new note: ${JSON.stringify(result)}`)
        }
      }

      return {
        success: true,
        noteKey,
        message: existingNoteKey ? 'Note updated successfully' : 'Note created successfully'
      }
    } catch (error) {
      console.error('Error creating/updating Zotero note:', error)

      // Provide more specific error handling for note operations
      let errorMessage = 'Unknown error occurred'
      if (error instanceof Error) {
        if (error.message.includes('Precondition Required')) {
          errorMessage = 'Version conflict: Unable to update existing note due to version mismatch'
        } else if (error.message.includes('Cannot read properties of undefined')) {
          errorMessage = 'Data structure error: Missing required data for note creation'
        } else {
          errorMessage = error.message
        }
      }

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }

  private formatNoteContent(note: Note): string {
    let content = '<div>'

    if (note.quickSummary) {
      const safeSummary = this.escapeHtml(note.quickSummary)
      content += `<h2>Quick Summary</h2><p>${safeSummary}</p>`
    }

    if (note.keyIdeas && note.keyIdeas.length > 0) {
      content += '<h2>Key Ideas</h2><ul>'
      note.keyIdeas.forEach(idea => {
        const safeIdea = this.escapeHtml(idea)
        content += `<li>${safeIdea}</li>`
      })
      content += '</ul>'
    }

    content += '<p><em>Synced from PaperNugget</em></p>'
    content += '</div>'

    return content
  }

  async getGroups(): Promise<Array<{
    id: string
    name: string
    description?: string
    type: string
    memberCount?: number
  }>> {
    try {
      // First get the user ID from the API key
      const keyResponse = await this.makeRequest('GET', '/keys/current')
      if (!keyResponse.ok) {
        throw new Error('Failed to get API key information')
      }

      const keyData = await keyResponse.json()
      if (!keyData.userID) {
        throw new Error('Could not get user ID from API key')
      }

      // Now fetch groups using the correct endpoint with user ID
      const response = await this.makeRequest('GET', `/users/${keyData.userID}/groups`)

      if (!response.ok) {
        throw new Error(`Failed to fetch groups: ${response.status}`)
      }

      const groups = await response.json()

      if (!Array.isArray(groups)) {
        return []
      }

      return groups.map((group: any) => ({
        id: group.id.toString(),
        name: group.data?.name || 'Unknown Group',
        description: group.data?.description || undefined,
        type: group.data?.type || 'Unknown',
        memberCount: group.meta?.numMembers || undefined
      })).filter(group => group.name !== 'Unknown Group') // Filter out malformed groups
    } catch (error) {
      console.error('Error fetching Zotero groups:', error)
      throw error
    }
  }

  /**
   * Search for a paper only in the default Zotero library (user's personal library)
   * Returns the first match found with library information
   */
  async searchPaperInDefaultLibrary(title: string, doi?: string, authors?: string[]): Promise<{
    item: ZoteroItem
    libraryType: 'user' | 'group'
    libraryId: string
    libraryName: string
  } | null> {
    try {
      console.log('ZoteroClient: Starting search in default library only', {
        title,
        doi,
        authorsCount: authors?.length || 0,
        hasApiKey: !!this.apiKey,
        apiKeyLength: this.apiKey?.length
      })

      // Search only in user library (default library)
      console.log('ZoteroClient: Searching in user library (default)')
      let foundItem = null

      // Try DOI search first if available
      if (doi) {
        console.log('ZoteroClient: Searching by DOI in user library', { doi })
        try {
          foundItem = await this.findItemByDOI('user', '', doi)
          if (foundItem) {
            console.log('ZoteroClient: Found item by DOI in user library', {
              doi,
              itemKey: foundItem.key,
              itemTitle: foundItem.title
            })
            return {
              item: foundItem,
              libraryType: 'user',
              libraryId: 'user',
              libraryName: 'My Library'
            }
          }
        } catch (doiError: any) {
          console.error('ZoteroClient: Error searching by DOI in user library', {
            doi,
            error: doiError.message,
            errorStack: doiError.stack
          })
        }
      }

      // Try title search in user library
      console.log('ZoteroClient: Searching by title in user library', { title })
      try {
        foundItem = await this.findItemByTitle('user', '', title)
        if (foundItem) {
          console.log('ZoteroClient: Found item by title in user library', {
            title,
            itemKey: foundItem.key,
            itemTitle: foundItem.title
          })
          return {
            item: foundItem,
            libraryType: 'user',
            libraryId: 'user',
            libraryName: 'My Library'
          }
        }
      } catch (titleError: any) {
        console.error('ZoteroClient: Error searching by title in user library', {
          title,
          error: titleError.message,
          errorStack: titleError.stack
        })
      }

      console.log('ZoteroClient: Paper not found in default library', {
        title,
        doi,
        authorsCount: authors?.length || 0
      })

      return null
    } catch (error: any) {
      console.error('ZoteroClient: Error during default library search', {
        title,
        doi,
        error: error.message,
        errorStack: error.stack
      })
      return null
    }
  }

  /**
   * Search for a paper across all accessible Zotero libraries (user library + groups)
   * Returns the first match found with library information
   */
  async searchPaperInAllLibraries(title: string, doi?: string, authors?: string[]): Promise<{
    item: ZoteroItem
    libraryType: 'user' | 'group'
    libraryId: string
    libraryName: string
  } | null> {
    try {
      console.log('ZoteroClient: Starting search across all libraries', {
        title,
        doi,
        authorsCount: authors?.length || 0,
        hasApiKey: !!this.apiKey,
        apiKeyLength: this.apiKey?.length
      })

      // First search in user library
      console.log('ZoteroClient: Searching in user library')
      let foundItem = null

      // Try DOI search first if available
      if (doi) {
        console.log('ZoteroClient: Searching by DOI in user library', { doi })
        try {
          foundItem = await this.findItemByDOI('user', '', doi)
          if (foundItem) {
            console.log('ZoteroClient: Found item by DOI in user library', {
              doi,
              itemKey: foundItem.key,
              itemTitle: foundItem.title
            })
            return {
              item: foundItem,
              libraryType: 'user',
              libraryId: 'user',
              libraryName: 'My Library'
            }
          }
        } catch (doiError: any) {
          console.error('ZoteroClient: Error searching by DOI in user library', {
            doi,
            error: doiError.message,
            errorStack: doiError.stack
          })
        }
      }

      // Try title search in user library
      console.log('ZoteroClient: Searching by title in user library', { title })
      try {
        foundItem = await this.findItemByTitle('user', '', title)
        if (foundItem) {
          console.log('ZoteroClient: Found item by title in user library', {
            title,
            itemKey: foundItem.key,
            itemTitle: foundItem.title
          })
          return {
            item: foundItem,
            libraryType: 'user',
            libraryId: 'user',
            libraryName: 'My Library'
          }
        }
      } catch (titleError: any) {
        console.error('ZoteroClient: Error searching by title in user library', {
          title,
          error: titleError.message,
          errorStack: titleError.stack
        })
      }

      // Search in all accessible groups
      console.log('ZoteroClient: Paper not found in user library, searching groups')

      let groups: any[] = []
      try {
        groups = await this.getGroups()
        console.log('ZoteroClient: Retrieved groups for search', {
          groupCount: groups.length,
          groupNames: groups.map(g => g.name)
        })
      } catch (groupsError: any) {
        console.error('ZoteroClient: Error retrieving groups', {
          error: groupsError.message,
          errorStack: groupsError.stack
        })
        return null
      }

      for (const group of groups) {
        console.log('ZoteroClient: Searching in group', {
          groupName: group.name,
          groupId: group.id,
          title,
          doi
        })

        // Try DOI search first if available
        if (doi) {
          try {
            foundItem = await this.findItemByDOI('group', group.id, doi)
            if (foundItem) {
              console.log('ZoteroClient: Found item by DOI in group', {
                doi,
                groupName: group.name,
                groupId: group.id,
                itemKey: foundItem.key,
                itemTitle: foundItem.title
              })
              return {
                item: foundItem,
                libraryType: 'group',
                libraryId: group.id,
                libraryName: group.name
              }
            }
          } catch (groupDoiError: any) {
            console.error('ZoteroClient: Error searching by DOI in group', {
              doi,
              groupName: group.name,
              groupId: group.id,
              error: groupDoiError.message
            })
          }
        }

        // Try title search
        try {
          foundItem = await this.findItemByTitle('group', group.id, title)
          if (foundItem) {
            console.log('ZoteroClient: Found item by title in group', {
              title,
              groupName: group.name,
              groupId: group.id,
              itemKey: foundItem.key,
              itemTitle: foundItem.title
            })
            return {
              item: foundItem,
              libraryType: 'group',
              libraryId: group.id,
              libraryName: group.name
            }
          }
        } catch (groupTitleError: any) {
          console.error('ZoteroClient: Error searching by title in group', {
            title,
            groupName: group.name,
            groupId: group.id,
            error: groupTitleError.message
          })
        }
      }

      console.log('ZoteroClient: Paper not found in any library', {
        title,
        doi,
        searchedUserLibrary: true,
        searchedGroupsCount: groups.length
      })
      return null
    } catch (error: any) {
      console.error('ZoteroClient: Critical error searching paper in libraries', {
        error: error.message,
        errorName: error.name,
        errorStack: error.stack,
        title,
        doi,
        authorsCount: authors?.length || 0
      })
      return null
    }
  }

  /**
   * Convert a Zotero item to our Paper metadata format for enrichment
   */
  convertZoteroItemToPaperMetadata(item: ZoteroItem): any {
    const metadata: any = {}

    // Basic fields
    if (item.title) metadata.title = item.title
    if (item.DOI) metadata.doi = item.DOI
    if (item.url) metadata.url = item.url
    if (item.abstractNote) metadata.abstract = item.abstractNote

    // Authors - handle different Zotero creator types
    if (item.creators && Array.isArray(item.creators)) {
      const authors = item.creators
        .filter(creator => creator.creatorType === 'author' || !creator.creatorType)
        .map(creator => {
          if (creator.name) return creator.name
          if (creator.firstName && creator.lastName) {
            return `${creator.firstName} ${creator.lastName}`
          }
          return creator.lastName || creator.firstName || ''
        })
        .filter(Boolean)

      if (authors.length > 0) metadata.authors = authors
    }

    // Publication details
    if (item.publicationTitle) metadata.venue = item.publicationTitle
    if (item.journalAbbreviation) metadata.journal = item.journalAbbreviation
    if (item.volume) metadata.volume = item.volume
    if (item.issue) metadata.issue = item.issue
    if (item.pages) metadata.pages = item.pages

    // Date handling
    if (item.date) {
      // Try to extract year from date
      const yearMatch = item.date.match(/\b(19|20)\d{2}\b/)
      if (yearMatch) {
        metadata.year = parseInt(yearMatch[0])
      }
      metadata.publicationDate = item.date
    }

    // Tags
    if (item.tags && Array.isArray(item.tags)) {
      metadata.tags = item.tags.map(tag => typeof tag === 'string' ? tag : tag.tag).filter(Boolean)
    }

    // Item type mapping to our paper types
    if (item.itemType) {
      metadata.paperType = item.itemType
    }

    return metadata
  }

  async testConnection(libraryType?: 'user' | 'group', libraryId?: string): Promise<boolean> {
    try {
      let endpoint: string

      if (libraryType === 'group' && libraryId) {
        // For group libraries, test access to the specific group
        endpoint = `/groups/${libraryId}`
      } else {
        // For user library, get the actual user ID
        const userId = await this.getUserId()
        endpoint = `/users/${userId}`
      }

      const response = await this.makeRequest('GET', endpoint)
      return response.ok
    } catch (error) {
      console.error('Error testing Zotero connection:', error)

      // If group library test fails, try fallback to user library
      if (libraryType === 'group') {
        try {
          console.log('Group library test failed, trying user library fallback...')
          const userId = await this.getUserId()
          const fallbackResponse = await this.makeRequest('GET', `/users/${userId}`)
          return fallbackResponse.ok
        } catch (fallbackError) {
          console.error('Fallback user library test also failed:', fallbackError)
          return false
        }
      }

      return false
    }
  }
}

export function createZoteroClient(apiKey: string): ZoteroClient {
  return new ZoteroClient(apiKey)
}
