'use client'

import { useAuth } from '@/lib/auth-context'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import PublicGettingStarted from '@/components/public/PublicGettingStarted'

export default function HomePage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && user) {
      // If user is logged in, redirect to dashboard
      router.push('/dashboard')
    }
  }, [user, isLoading, router])

  // If user is logged in, show loading while redirecting
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse">Redirecting to dashboard...</div>
      </div>
    )
  }

  // If not logged in, show public getting started page
  return <PublicGettingStarted />
}

// Force dynamic rendering to avoid SSR issues with authentication
export const dynamic = 'force-dynamic'
