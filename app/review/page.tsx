"use client"

import { useState, useEffect } from "react"
import { CheckCircle, RotateCcw, ArrowLeft, ArrowRight, Keyboard, Presentation, BookOpenCheck, Play } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ConditionalSidebarTrigger } from "@/components/ui/conditional-sidebar-trigger"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/hooks/use-toast"
import { useLoggedFetch } from "@/hooks/use-logged-fetch"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { AuthModal } from "@/components/auth/AuthModal"
import type { Paper, Review, Note } from "@/lib/types"

interface ReviewStats {
  totalPapers: number
  totalReviews: number
  dueToday: number
  dueThisWeek: number
  overdue: number
  averageEase: number
  retentionRate: number
  intervalDistribution: {
    today: number
    tomorrow: number
    thisWeek: number
    later: number
  }
}

function ReviewPageContent() {
  const { toast } = useToast()
  const { fetch: loggedFetch } = useLoggedFetch('ReviewPageContent')
  const [duePapers, setDuePapers] = useState<(Paper & { review: Review; note?: Note })[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState<ReviewStats | null>(null)

  useEffect(() => {
    console.log("Review page mounted, fetching data...")
    fetchDuePapers()
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await loggedFetch("/api/review/stats", {}, 'fetch_review_stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      } else {
        console.error("ReviewPageContent: Failed to fetch review stats:", response.status, response.statusText)
      }
    } catch (error) {
      console.error("ReviewPageContent: Failed to fetch review stats:", error)
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle shortcuts when typing in inputs
      const target = e.target as HTMLElement
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        return
      }

      switch (e.key.toLowerCase()) {
        case "g":
        case "enter":
          e.preventDefault()
          if (currentPaper) {
            updateReview(currentPaper.id, true)
          }
          break
        case "r":
        case " ": // spacebar
          e.preventDefault()
          if (currentPaper) {
            updateReview(currentPaper.id, false)
          }
          break
        case "arrowleft":
        case "h":
          e.preventDefault()
          if (currentIndex > 0) {
            setCurrentIndex(currentIndex - 1)
          }
          break
        case "arrowright":
        case "l":
          e.preventDefault()
          if (currentIndex < duePapers.length - 1) {
            setCurrentIndex(currentIndex + 1)
          }
          break
        case "escape":
          e.preventDefault()
          window.location.href = "/papers"
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [currentIndex, duePapers.length])

  const fetchDuePapers = async () => {
    try {
      console.log("ReviewPageContent: Fetching due papers...")
      setIsLoading(true)
      const response = await loggedFetch("/api/review/due", {}, 'fetch_due_papers')
      console.log("ReviewPageContent: Response status:", response.status)
      if (response.ok) {
        const data = await response.json()
        console.log("ReviewPageContent: Due papers data:", data)
        setDuePapers(Array.isArray(data) ? data : [])
      } else {
        console.error("ReviewPageContent: Failed to fetch due papers:", response.status, response.statusText)
        toast({ title: "Failed to fetch due papers", variant: "destructive" })
        setDuePapers([])
      }
    } catch (error) {
      console.error("ReviewPageContent: Failed to fetch due papers:", error)
      toast({ title: "Failed to fetch due papers", variant: "destructive" })
      setDuePapers([])
    } finally {
      setIsLoading(false)
    }
  }

  const updateReview = async (paperId: string, gotIt: boolean) => {
    try {
      const response = await fetch(`/api/review/${paperId}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ gotIt }),
      })

      if (response.ok) {
        // Show success message
        toast({
          title: gotIt ? "Marked as reviewed!" : "Scheduled for revisit",
          description: gotIt ? "Great job! Keep it up." : "This paper will appear again soon."
        })

        // Remove from current list and move to next
        setDuePapers((prev) => prev.filter((p) => p.id !== paperId))
        if (currentIndex >= duePapers.length - 1) {
          setCurrentIndex(Math.max(0, currentIndex - 1))
        }
      } else {
        throw new Error("Failed to update review")
      }
    } catch (error) {
      console.error("Failed to update review:", error)
      toast({ title: "Failed to update review", variant: "destructive" })
    }
  }

  const currentPaper = duePapers[currentIndex]

  const nextPaper = () => {
    if (currentIndex < duePapers.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const prevPaper = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  if (isLoading) {
    return (
      <div className="flex flex-col h-screen">
        <header className="border-b p-4">
          <div className="flex items-center gap-4">
            <ConditionalSidebarTrigger />
            <h1 className="text-2xl font-bold">Review</h1>
          </div>
        </header>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading due papers...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!isLoading && duePapers.length === 0) {
    return (
      <div className="flex flex-col h-screen">
        <header className="border-b p-4">
          <div className="flex items-center gap-4">
            <ConditionalSidebarTrigger />
            <h1 className="text-2xl font-bold">Review</h1>
          </div>
        </header>
        <div className="flex-1 overflow-auto p-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">All caught up!</h2>
              <p className="text-muted-foreground mb-4">No papers due for review right now.</p>
              <Button
                onClick={() => window.location.href = '/review/session?includeAll=true'}
                variant="outline"
                size="lg"
              >
                <Play className="h-5 w-5 mr-2" />
                Start Review Session Anyway
              </Button>
            </div>

            {stats && (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{stats.totalPapers}</div>
                    <p className="text-sm text-muted-foreground">Total Papers</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{stats.dueThisWeek}</div>
                    <p className="text-sm text-muted-foreground">Due This Week</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{stats.averageEase}</div>
                    <p className="text-sm text-muted-foreground">Average Ease</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{stats.retentionRate}%</div>
                    <p className="text-sm text-muted-foreground">Retention Rate</p>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Handle case where currentPaper is undefined
  if (!currentPaper) {
    return (
      <div className="flex flex-col h-screen">
        <header className="border-b p-4">
          <div className="flex items-center gap-4">
            <ConditionalSidebarTrigger />
            <h1 className="text-2xl font-bold">Review</h1>
          </div>
        </header>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading review...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <ConditionalSidebarTrigger />
          <h1 className="text-2xl font-bold">Review</h1>
          <div className="flex-1" />
          <Button
            onClick={() => window.location.href = '/review/session'}
            size="lg"
            className="mr-4 bg-green-600 hover:bg-green-700 text-white"
          >
            <Play className="h-5 w-5 mr-2" />
            Start Review Session ({duePapers.length} papers)
          </Button>
          <Badge variant="secondary">
            {currentIndex + 1} of {duePapers.length} due
          </Badge>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={prevPaper}
              disabled={currentIndex === 0}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={nextPaper}
              disabled={currentIndex === duePapers.length - 1}
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="mt-2">
          <Progress value={(currentIndex + 1) / duePapers.length * 100} className="h-2" />
        </div>
      </header>

      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-4xl mx-auto">
          <div className="transition-all duration-300 ease-in-out">
            <Card className="shadow-lg">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-2xl leading-tight">{currentPaper.title}</CardTitle>
                    <p className="text-lg text-muted-foreground mt-2">{currentPaper.authors.join(", ")}</p>
                    {currentPaper.venue && (
                      <p className="text-muted-foreground mt-1">
                        {currentPaper.venue} {currentPaper.year && `(${currentPaper.year})`}
                      </p>
                    )}
                  </div>
                  {currentPaper.starred && (
                    <div className="ml-4">
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        ⭐ Starred
                      </Badge>
                    </div>
                  )}
                </div>
                <div className="flex gap-2 mt-4">
                  {currentPaper.tags?.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  )) || []}
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                {currentPaper.note && (
                  <div className="bg-muted/30 rounded-lg p-6">
                    {/* Quick Summary */}
                    {currentPaper.note.quickSummary && (
                      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-950/30 rounded-md border-l-4 border-blue-500">
                        <h4 className="font-semibold mb-2 text-blue-900 dark:text-blue-100">📝 Quick Summary</h4>
                        <p className="leading-relaxed text-blue-800 dark:text-blue-200">{currentPaper.note.quickSummary}</p>
                      </div>
                    )}

                    {/* Key Ideas */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        💡 Key Ideas
                      </h3>
                      {currentPaper.note.keyIdeas?.filter(Boolean).map((idea, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-background rounded-md">
                          <span className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold shrink-0 mt-1">
                            {index + 1}
                          </span>
                          <p className="leading-relaxed">{idea}</p>
                        </div>
                      )) || []}
                    </div>
                  </div>
                )}

                {!currentPaper.note && (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No notes available for this paper.</p>
                    <p className="text-sm mt-2">Consider adding notes to improve your review experience.</p>
                  </div>
                )}

                <div className="space-y-4 pt-6 border-t">
                  <div className="flex gap-4">
                    <Button
                      onClick={() => updateReview(currentPaper.id, true)}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                      size="lg"
                    >
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Got it!
                      <kbd className="ml-2 px-2 py-1 bg-white/20 rounded text-xs">G</kbd>
                    </Button>
                    <Button
                      onClick={() => updateReview(currentPaper.id, false)}
                      variant="outline"
                      className="flex-1 border-orange-300 text-orange-700 hover:bg-orange-50"
                      size="lg"
                    >
                      <RotateCcw className="h-5 w-5 mr-2" />
                      Revisit
                      <kbd className="ml-2 px-2 py-1 bg-muted rounded text-xs">R</kbd>
                    </Button>
                  </div>

                  <div className="text-center text-sm text-muted-foreground bg-muted/30 rounded-lg p-3">
                    <div className="flex items-center justify-center gap-4 flex-wrap">
                      <span><kbd className="px-2 py-1 bg-background rounded text-xs">←/→</kbd> Navigate</span>
                      <span><kbd className="px-2 py-1 bg-background rounded text-xs">G</kbd> Got it</span>
                      <span><kbd className="px-2 py-1 bg-background rounded text-xs">R</kbd> Revisit</span>
                      <span><kbd className="px-2 py-1 bg-background rounded text-xs">ESC</kbd> Exit</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ReviewPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            // Redirect to login page instead of just closing
            window.location.href = '/login'
          }}
        />
      }
    >
      <ReviewPageContent />
    </ProtectedRoute>
  )
}
