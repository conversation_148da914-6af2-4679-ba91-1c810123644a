import { NextRequest, NextResponse } from 'next/server'
import { papers, notes, collections, reviews, users, auditLogs } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createErrorResponse } from '@/lib/validation'
import { sanitizeUser } from '@/lib/auth'

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, user, userId, logger }) => {
  try {
    logger.info('User requesting data export', { userId })

    // Get all user data
    const [
      userPapers,
      userNotes,
      userCollections,
      userReviews
    ] = await Promise.all([
      papers.getByUserId(userId),
      notes.getByUserId(userId),
      collections.getByUserId(userId),
      reviews.getByUserId(userId)
    ])

    // Create export data structure
    const exportData = {
      metadata: {
        exportedAt: new Date().toISOString(),
        userId: userId,
        userEmail: user.email,
        version: '1.0',
        description: 'PaperNugget user data export'
      },
      user: sanitize<PERSON>ser(user),
      data: {
        papers: userPapers,
        notes: userNotes,
        collections: userCollections,
        reviews: userReviews
      },
      statistics: {
        totalPapers: userPapers.length,
        totalNotes: userNotes.length,
        totalCollections: userCollections.length,
        totalReviews: userReviews.length,
        starredPapers: userPapers.filter(p => p.starred).length,
        papersWithNotes: userNotes.length,
        collectionsWithPapers: userCollections.filter(c => c.paperIds.length > 0).length
      }
    }

    // Log the export action
    await auditLogs.create({
      userId,
      action: 'user_data_export',
      resourceType: 'user',
      resourceId: userId,
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      details: {
        exportSize: JSON.stringify(exportData).length,
        statistics: exportData.statistics
      }
    })

    logger.info('User data export completed', { 
      userId, 
      exportSize: JSON.stringify(exportData).length,
      statistics: exportData.statistics
    })

    // Return the export data as JSON
    const response = NextResponse.json(exportData)
    
    // Set headers for file download
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `papernugget-export-${user.email.replace('@', '-at-')}-${timestamp}.json`
    response.headers.set('Content-Disposition', `attachment; filename="${filename}"`)
    response.headers.set('Content-Type', 'application/json')
    
    return response

  } catch (error: any) {
    logger.error('Error exporting user data', { error: error.message, userId })
    return createErrorResponse('Failed to export user data', 500, correlationId)
  }
}, {}, {
  allowUnverified: true,
  auditLog: {
    action: 'user_data_export_request',
    resourceType: 'user'
  }
})
