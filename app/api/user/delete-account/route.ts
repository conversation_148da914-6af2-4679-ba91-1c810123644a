import { NextRequest, NextResponse } from 'next/server'
import { users, papers, notes, collections, reviews, userSessions, emailVerificationTokens, passwordResetTokens, auditLogs } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createErrorResponse, createSuccessResponse } from '@/lib/validation'
import { sendAccountDeletionEmail, type DeletionResult } from '@/lib/mailer'
import { query } from '@/lib/db'

interface DeletionProgress {
  papers: DeletionResult
  notes: DeletionResult
  collections: DeletionResult
  reviews: DeletionResult
  sessions: DeletionResult
  tokens: DeletionResult
  passwordHistory: DeletionResult
  userAccount: DeletionR<PERSON>ult
}

async function deleteUserDataWithProgress(userId: string, userEmail: string): Promise<DeletionProgress> {
  const progress: DeletionProgress = {
    papers: { type: 'Papers', status: 'in_progress' },
    notes: { type: 'Notes', status: 'in_progress' },
    collections: { type: 'Collections', status: 'in_progress' },
    reviews: { type: 'Reviews', status: 'in_progress' },
    sessions: { type: 'User Sessions', status: 'in_progress' },
    tokens: { type: 'Verification & Reset Tokens', status: 'in_progress' },
    passwordHistory: { type: 'Password History', status: 'in_progress' },
    userAccount: { type: 'User Account', status: 'in_progress' }
  }

  try {
    // 1. Delete reviews (depends on papers)
    try {
      const userReviews = await reviews.getByUserId(userId)
      for (const review of userReviews) {
        await reviews.delete(review.paperId)
      }
      progress.reviews = { type: 'Reviews', status: 'done', count: userReviews.length }
    } catch (error) {
      progress.reviews = { type: 'Reviews', status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // 2. Delete notes (depends on papers)
    try {
      const userNotes = await notes.getByUserId(userId)
      for (const note of userNotes) {
        await notes.delete(note.paperId) // notes.delete uses paperId, not note.id
      }
      progress.notes = { type: 'Notes', status: 'done', count: userNotes.length }
    } catch (error) {
      progress.notes = { type: 'Notes', status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // 3. Delete collections
    try {
      const userCollections = await collections.getByUserId(userId)
      for (const collection of userCollections) {
        await collections.delete(collection.id)
      }
      progress.collections = { type: 'Collections', status: 'done', count: userCollections.length }
    } catch (error) {
      progress.collections = { type: 'Collections', status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // 4. Delete papers
    try {
      const userPapers = await papers.getByUserId(userId)
      for (const paper of userPapers) {
        await papers.delete(paper.id)
      }
      progress.papers = { type: 'Papers', status: 'done', count: userPapers.length }
    } catch (error) {
      progress.papers = { type: 'Papers', status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // 5. Delete user sessions
    try {
      const sessions = await userSessions.getByUserId(userId)
      for (const session of sessions) {
        await userSessions.deleteByTokenHash(session.tokenHash)
      }
      progress.sessions = { type: 'User Sessions', status: 'done', count: sessions.length }
    } catch (error) {
      progress.sessions = { type: 'User Sessions', status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // 6. Delete verification and reset tokens
    try {
      let tokenCount = 0
      
      // Delete email verification tokens
      const emailTokensResult = await query('DELETE FROM email_verification_tokens WHERE user_id = $1', [userId])
      tokenCount += emailTokensResult.rowCount || 0
      
      // Delete password reset tokens
      const resetTokensResult = await query('DELETE FROM password_reset_tokens WHERE user_id = $1', [userId])
      tokenCount += resetTokensResult.rowCount || 0
      
      progress.tokens = { type: 'Verification & Reset Tokens', status: 'done', count: tokenCount }
    } catch (error) {
      progress.tokens = { type: 'Verification & Reset Tokens', status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // 7. Delete password history
    try {
      const passwordHistoryResult = await query('DELETE FROM password_history WHERE user_id = $1', [userId])
      progress.passwordHistory = { type: 'Password History', status: 'done', count: passwordHistoryResult.rowCount || 0 }
    } catch (error) {
      progress.passwordHistory = { type: 'Password History', status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // 8. Finally, delete the user account
    try {
      const deleted = await users.delete(userId)
      if (deleted) {
        progress.userAccount = { type: 'User Account', status: 'done', count: 1 }
      } else {
        progress.userAccount = { type: 'User Account', status: 'failed', error: 'User account deletion returned false' }
      }
    } catch (error) {
      progress.userAccount = { type: 'User Account', status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
    }

  } catch (error) {
    console.error('Unexpected error during user deletion:', error)
  }

  return progress
}

export const DELETE = withAuthSecurity(async (request: NextRequest, { correlationId, user, userId, logger }) => {
  try {
    logger.info('User requesting account deletion', { userId, email: user.email })

    // Get user email before deletion
    const userEmail = user.email

    // Log the deletion action BEFORE starting deletion (since audit_logs has FK constraint)
    try {
      await auditLogs.create({
        userId,
        action: 'account_deletion_started',
        resourceType: 'user',
        resourceId: userId,
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        details: {
          email: userEmail
        }
      })
    } catch (auditError) {
      logger.error('Failed to create audit log for account deletion start', {
        error: auditError instanceof Error ? auditError.message : 'Unknown error',
        userId
      })
      // Continue with deletion even if audit log fails
    }

    // Perform the deletion with progress tracking
    const deletionProgress = await deleteUserDataWithProgress(userId, userEmail)

    // Convert progress to results array for email
    const deletionResults: DeletionResult[] = Object.values(deletionProgress)



    // Send confirmation email
    try {
      await sendAccountDeletionEmail(userEmail, deletionResults)
      logger.info('Account deletion confirmation email sent', { email: userEmail })
    } catch (emailError) {
      logger.error('Failed to send account deletion confirmation email', {
        error: emailError instanceof Error ? emailError.message : 'Unknown error',
        email: userEmail
      })
      // Continue with deletion even if email fails
    }

    logger.info('Account deletion completed', { 
      email: userEmail,
      deletionSummary: deletionResults.map(r => `${r.type}: ${r.status}`)
    })

    // Return success response
    return createSuccessResponse({
      message: 'Account deleted successfully',
      deletionResults,
      email: userEmail
    }, correlationId)

  } catch (error: any) {
    logger.error('Error deleting user account', { error: error.message, userId })
    return createErrorResponse('Failed to delete account', 500, correlationId)
  }
}, {}, {
  allowUnverified: true,
  auditLog: {
    action: 'account_deletion_request',
    resourceType: 'user'
  }
})
