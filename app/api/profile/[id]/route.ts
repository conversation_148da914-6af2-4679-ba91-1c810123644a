import { NextRequest, NextResponse } from 'next/server'
import { users, papers, auditLogs } from '@/lib/database'
import { withPublicSecurity } from '@/lib/security-middleware'
import { validationSchemas } from '@/lib/validation-schemas'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { sanitizeUser } from '@/lib/auth'

export const GET = withPublicSecurity(async (request: NextRequest, { correlationId, validatedData, logger }, { params }: { params: Promise<{ id: string }> }) => {
  let id: string
  try {
    const paramsData = await params
    id = paramsData.id
    logger.info('Fetching public profile', { userId: id })

    // Get user by ID
    const user = await users.getById(id)
    if (!user) {
      return createErrorResponse('User not found', 404, correlationId)
    }

    // Check if user has enabled public profile
    const privacySettings = user.privacySettings || {}
    if (!privacySettings.profilePublic) {
      return createErrorResponse('Profile is private', 403, correlationId)
    }

    // Get user's papers
    const userPapers = await papers.getByUserId(id)

    // Sanitize user data for public view
    const publicProfile = {
      id: user.id,
      displayName: user.displayName,
      createdAt: user.createdAt,
      paperCount: userPapers.length,
      papers: userPapers.map(paper => ({
        id: paper.id,
        title: paper.title,
        authors: paper.authors,
        venue: paper.venue,
        year: paper.year,
        doi: paper.doi,
        url: paper.url,
        abstract: paper.abstract,
        citationCount: paper.citationCount,
        referenceCount: paper.referenceCount,
        publicationDate: paper.publicationDate,
        journal: paper.journal,
        volume: paper.volume,
        issue: paper.issue,
        pages: paper.pages,
        tags: paper.tags,
        starred: paper.starred,
        paperType: paper.paperType,
        createdAt: paper.createdAt,
        updatedAt: paper.updatedAt
      }))
    }

    // Log the public profile view
    await auditLogs.create({
      userId: undefined, // Anonymous access
      action: 'public_profile_viewed',
      resourceType: 'user',
      resourceId: id,
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      details: {
        profileUserId: id,
        paperCount: userPapers.length
      },
    })

    return createSuccessResponse(publicProfile, 'Public profile retrieved successfully', correlationId)
  } catch (error: any) {
    logger.error('Error fetching public profile', { error: error.message, userId: id })
    return createErrorResponse('Failed to fetch public profile', 500, correlationId)
  }
}, {
  params: validationSchemas.user.publicProfileParams
})
