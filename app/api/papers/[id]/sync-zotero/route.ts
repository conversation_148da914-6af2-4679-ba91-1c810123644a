import { NextRequest, NextResponse } from 'next/server'
import { papers } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { canAccessResource } from '@/lib/auth-middleware'
import { createZoteroSyncService, getZoteroSettingsFromPreferences, validateZoteroSettings } from '@/lib/zotero-sync-service'

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params

    // Parse request body for forced destination
    let requestBody: { forceDestination?: { libraryType?: 'user' | 'group'; libraryId?: string } } = {}
    try {
      const bodyText = await request.text()
      if (bodyText) {
        requestBody = JSON.parse(bodyText)
      }
    } catch (parseError) {
      // Ignore parsing errors for empty body
    }

    logger.info('=== ZOTERO SYNC REQUEST STARTED ===', {
      userId,
      paperId: id,
      correlationId,
      method: request.method,
      url: request.url,
      hasForceDestination: !!requestBody.forceDestination
    })
    logger.info('Syncing paper to Zotero', { userId, paperId: id, forceDestination: requestBody.forceDestination })

    // Get the paper
    logger.info('Attempting to fetch paper', { paperId: id })
    const paper = await papers.getById(id)
    logger.info('Paper fetch result', {
      paperId: id,
      paperFound: !!paper,
      paperUserId: paper?.userId,
      paperTitle: paper?.title?.substring(0, 50)
    })

    if (!paper) {
      logger.error('Paper not found for Zotero sync', { paperId: id })
      const errorResponse = createErrorResponse(
        'Paper not found',
        404,
        correlationId,
        undefined,
        'Paper does not exist'
      )
      logger.error('Returning 404 error response', { response: errorResponse })
      return errorResponse
    }

    // Check if user can access this paper
    logger.info('Checking paper access', {
      userId,
      paperId: id,
      paperUserId: paper.userId,
      userRole: user.role
    })

    const hasAccess = canAccessResource(user, paper.userId)
    logger.info('Access check result', {
      userId,
      paperId: id,
      hasAccess,
      paperUserId: paper.userId
    })

    if (!hasAccess) {
      logger.warn('Access denied to paper for Zotero sync', { userId, paperId: id, paperUserId: paper.userId })
      const errorResponse = createErrorResponse(
        'Access denied',
        403,
        correlationId,
        undefined,
        'You do not have permission to sync this paper'
      )
      logger.error('Returning 403 error response', { response: errorResponse })
      return errorResponse
    }

    // Get Zotero settings from user preferences
    logger.info('Getting Zotero settings from user preferences', {
      userId,
      paperId: id,
      hasPreferences: !!user.preferences,
      preferences: user.preferences
    })

    const zoteroSettings = getZoteroSettingsFromPreferences(user.preferences || {})

    logger.info('Zotero settings retrieved', {
      userId,
      paperId: id,
      enabled: zoteroSettings.enabled,
      hasApiKey: !!zoteroSettings.apiKey,
      hasUserId: !!zoteroSettings.userId,
      hasLibraryId: !!zoteroSettings.libraryId
    })
    
    if (!zoteroSettings.enabled) {
      logger.warn('Zotero sync not enabled for user', {
        userId,
        paperId: id,
        zoteroSettings
      })
      const errorResponse = createErrorResponse(
        'Zotero sync is disabled',
        400,
        correlationId,
        [
          'Zotero sync is currently disabled in your settings.',
          'To enable sync: Go to Settings → Zotero Integration → Enable Zotero Sync',
          'Make sure you have configured your API key and library settings.'
        ],
        'Please enable Zotero sync in your settings to use this feature'
      )
      logger.error('Returning 400 error response - Zotero not enabled', { response: errorResponse })
      return errorResponse
    }

    // Validate Zotero settings
    logger.info('Validating Zotero settings for paper sync', {
      userId,
      paperId: id,
      enabled: zoteroSettings.enabled,
      hasApiKey: !!zoteroSettings.apiKey,
      apiKeyLength: zoteroSettings.apiKey?.length,
      libraryType: zoteroSettings.libraryType,
      libraryId: zoteroSettings.libraryId
    })

    const validation = validateZoteroSettings(zoteroSettings)
    if (!validation.valid) {
      logger.error('Zotero settings validation failed', {
        userId,
        paperId: id,
        errors: validation.errors,
        settings: {
          enabled: zoteroSettings.enabled,
          hasApiKey: !!zoteroSettings.apiKey,
          apiKeyLength: zoteroSettings.apiKey?.length,
          libraryType: zoteroSettings.libraryType,
          libraryId: zoteroSettings.libraryId
        }
      })
      return createErrorResponse(
        'Invalid Zotero configuration',
        400,
        correlationId,
        validation.errors,
        'Configuration validation failed'
      )
    }

    try {
      // Create sync service and sync the paper
      const syncService = await createZoteroSyncService(zoteroSettings.apiKey!, zoteroSettings)
      const result = requestBody.forceDestination
        ? await syncService.syncPaperToSpecificDestination(id, requestBody.forceDestination)
        : await syncService.syncPaper(id)

      if (result.success) {
        logger.info('Paper synced to Zotero successfully', { 
          userId, 
          paperId: id, 
          itemKey: result.itemKey,
          noteKey: result.noteKey 
        })
        
        return createSuccessResponse({
          message: result.message,
          itemKey: result.itemKey,
          noteKey: result.noteKey,
          syncedAt: new Date().toISOString()
        }, correlationId)
      } else {
        logger.error('Failed to sync paper to Zotero', { 
          userId, 
          paperId: id, 
          error: result.error 
        })
        
        return createErrorResponse(
          'Sync failed',
          400,
          correlationId,
          [result.error || 'Unknown error occurred'],
          'Zotero sync failed'
        )
      }
    } catch (error) {
      logger.error('Error during Zotero sync', { error: error.message, userId, paperId: id })
      return createErrorResponse(
        'Sync failed',
        500,
        correlationId,
        ['An unexpected error occurred during sync'],
        'Internal sync error'
      )
    }
  } catch (error: any) {
    logger.error('Error in Zotero sync endpoint', {
      error: error.message,
      userId: userId || 'unknown',
      stack: error.stack,
      correlationId
    })
    const errorResponse = createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'Failed to sync paper to Zotero'
    )
    logger.error('Returning 500 error response', { response: errorResponse })
    return errorResponse
  }
}, undefined, {
  allowUnverified: true,
  auditLog: {
    action: 'paper_zotero_sync',
    resourceType: 'paper',
    getResourceId: (request, routeParams) => routeParams?.id || 'unknown'
  }
})
