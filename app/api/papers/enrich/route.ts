import { NextRequest, NextResponse } from "next/server"
import { findBestMatch, getPaperByDOI, convertSemanticScholarPaper } from "@/lib/semantic-scholar"
import { withAuthSecurity } from "@/lib/security-middleware"
import { validationSchemas } from "@/lib/validation-schemas"
import { createZoteroSyncService, getZoteroSettingsFromPreferences } from "@/lib/zotero-sync-service"
import { collections } from "@/lib/database"
import { ZoteroSettings } from "@/lib/types"

/**
 * Get Zotero settings for enrichment, considering collection-specific configurations
 */
async function getZoteroSettingsForEnrichment(
  userPreferences: Record<string, any>,
  collectionId: string | undefined,
  userId: string,
  logger: any
): Promise<ZoteroSettings> {
  logger.info('Starting Zotero settings retrieval', {
    userId,
    collectionId,
    hasUserPreferences: !!userPreferences,
    userPreferencesStructure: userPreferences ? {
      hasZotero: !!userPreferences.zotero,
      zoteroKeys: userPreferences.zotero ? Object.keys(userPreferences.zotero) : []
    } : null
  })

  // Start with global user settings
  const globalSettings = getZoteroSettingsFromPreferences(userPreferences)

  logger.info('Global Zotero settings parsed', {
    userId,
    enabled: globalSettings.enabled,
    hasApiKey: !!globalSettings.apiKey,
    apiKeyLength: globalSettings.apiKey?.length,
    libraryType: globalSettings.libraryType,
    libraryId: globalSettings.libraryId
  })

  // If no collection specified, use global settings
  if (!collectionId) {
    logger.info('No collection context, using global Zotero settings', {
      userId,
      enabled: globalSettings.enabled,
      hasApiKey: !!globalSettings.apiKey,
      libraryType: globalSettings.libraryType
    })
    return globalSettings
  }

  try {
    logger.info('Fetching collection for Zotero settings', { userId, collectionId })

    // Get the collection to check for specific Zotero configuration
    const collection = await collections.getById(collectionId)

    if (!collection) {
      logger.warn('Collection not found, using global settings', {
        userId,
        collectionId,
        globalSettingsEnabled: globalSettings.enabled
      })
      return globalSettings
    }

    logger.info('Collection retrieved', {
      userId,
      collectionId,
      collectionName: collection.name,
      collectionUserId: collection.userId,
      hasZoteroLibraryType: !!collection.zoteroLibraryType,
      zoteroLibraryType: collection.zoteroLibraryType,
      hasZoteroLibraryId: !!collection.zoteroLibraryId,
      zoteroLibraryId: collection.zoteroLibraryId
    })

    // Check if collection has specific Zotero destination configured
    if (collection.zoteroLibraryType &&
        (collection.zoteroLibraryType === 'user' || collection.zoteroLibraryId)) {

      const collectionSettings: ZoteroSettings = {
        ...globalSettings,
        libraryType: collection.zoteroLibraryType,
        libraryId: collection.zoteroLibraryType === 'user' ? 'user' : collection.zoteroLibraryId!
      }

      logger.info('Using collection-specific Zotero settings', {
        userId,
        collectionId,
        collectionName: collection.name,
        libraryType: collectionSettings.libraryType,
        libraryId: collectionSettings.libraryId,
        enabled: collectionSettings.enabled,
        hasApiKey: !!collectionSettings.apiKey,
        apiKeyLength: collectionSettings.apiKey?.length
      })

      return collectionSettings
    }

    logger.info('Collection has no specific Zotero config, using global settings', {
      userId,
      collectionId,
      collectionName: collection.name,
      globalEnabled: globalSettings.enabled
    })
    return globalSettings

  } catch (error: any) {
    logger.error('Error getting collection for Zotero settings, falling back to global', {
      userId,
      collectionId,
      error: error.message,
      errorStack: error.stack,
      errorName: error.name
    })
    return globalSettings
  }
}

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    // Log the complete request payload for debugging
    logger.info('Paper enrichment request received', {
      userId,
      correlationId,
      requestBody: validatedData.body,
      userPreferences: user.preferences,
      timestamp: new Date().toISOString()
    })

    const { title, authors, doi, collectionId } = validatedData.body

    if (!title && !doi) {
      logger.error('Validation failed: Missing required fields', {
        userId,
        correlationId,
        title: !!title,
        doi: !!doi,
        requestBody: validatedData.body
      })
      return NextResponse.json({ error: "Title or DOI is required", correlationId }, { status: 400 })
    }

    logger.info('Enriching paper with priority system', {
      userId,
      title,
      authors,
      doi,
      collectionId,
      hasCollectionContext: !!collectionId,
      correlationId
    })

    let zoteroMetadata = null
    let semanticScholarMetadata = null
    let enrichedMetadata = null
    let source = null
    let zoteroLibraryInfo = null

    // STEP 1: Try to get basic metadata from Zotero default library
    try {
      logger.info('Getting Zotero settings for enrichment', {
        userId,
        correlationId,
        collectionId,
        hasUserPreferences: !!user.preferences,
        userPreferencesKeys: user.preferences ? Object.keys(user.preferences) : []
      })

      const zoteroSettings = await getZoteroSettingsForEnrichment(
        user.preferences || {},
        collectionId,
        userId,
        logger
      )

      logger.info('Zotero settings retrieved', {
        userId,
        correlationId,
        enabled: zoteroSettings.enabled,
        hasApiKey: !!zoteroSettings.apiKey,
        apiKeyLength: zoteroSettings.apiKey?.length,
        libraryType: zoteroSettings.libraryType,
        libraryId: zoteroSettings.libraryId,
        collectionId
      })

      if (zoteroSettings.enabled && zoteroSettings.apiKey) {
        logger.info('Attempting Zotero enrichment from default library', {
          userId,
          correlationId,
          hasApiKey: !!zoteroSettings.apiKey,
          libraryType: zoteroSettings.libraryType,
          libraryId: zoteroSettings.libraryId
        })

        const syncService = await createZoteroSyncService(zoteroSettings.apiKey, zoteroSettings)

        logger.info('Searching paper in Zotero default library', {
          userId,
          correlationId,
          title,
          hasDoi: !!doi,
          authorsCount: authors?.length || 0
        })

        const zoteroResult = await syncService.searchPaperInDefaultLibrary(title, doi, authors)

        if (zoteroResult) {
          zoteroMetadata = zoteroResult.metadata
          zoteroLibraryInfo = {
            libraryType: zoteroResult.libraryType,
            libraryId: zoteroResult.libraryId,
            libraryName: zoteroResult.libraryName
          }

          logger.info('Paper found in Zotero default library', {
            userId,
            correlationId,
            source: zoteroResult.source,
            libraryType: zoteroResult.libraryType,
            libraryName: zoteroResult.libraryName,
            metadataFields: Object.keys(zoteroResult.metadata || {})
          })
        } else {
          logger.info('Paper not found in Zotero default library', {
            userId,
            correlationId,
            title,
            doi
          })
        }
      } else {
        logger.info('Zotero not configured, skipping Zotero enrichment', {
          userId,
          correlationId,
          enabled: zoteroSettings.enabled,
          hasApiKey: !!zoteroSettings.apiKey,
          reason: !zoteroSettings.enabled ? 'disabled' : 'no_api_key'
        })
      }
    } catch (zoteroError: any) {
      logger.error('Zotero search failed', {
        userId,
        correlationId,
        collectionId,
        error: zoteroError.message,
        errorStack: zoteroError.stack,
        errorName: zoteroError.name,
        title,
        doi
      })
    }

    // STEP 2: Try to get citation data from Semantic Scholar
    logger.info('Attempting Semantic Scholar enrichment', { userId, correlationId, hasDoi: !!doi, hasTitle: !!title, hasAuthors: <AUTHORS>

    let semanticScholarPaper = null
    let searchMethod = null

    // Try to find by DOI first if available
    if (doi) {
      searchMethod = 'DOI'
      semanticScholarPaper = await getPaperByDOI(doi)
      logger.info('Semantic Scholar DOI search result', { userId, correlationId, doi, found: !!semanticScholarPaper })
    }

    // If not found by DOI, try searching by title and authors
    if (!semanticScholarPaper && title) {
      searchMethod = 'title_and_authors'
      semanticScholarPaper = await findBestMatch(title, authors)
      logger.info('Semantic Scholar title/author search result', {
        userId,
        correlationId,
        title,
        authorCount: authors?.length || 0,
        found: !!semanticScholarPaper
      })
    }

    if (semanticScholarPaper) {
      semanticScholarMetadata = convertSemanticScholarPaper(semanticScholarPaper)
      logger.info('Paper found in Semantic Scholar', {
        userId,
        correlationId,
        searchMethod,
        citationCount: semanticScholarMetadata.citationCount,
        referenceCount: semanticScholarMetadata.referenceCount,
        doi: semanticScholarMetadata.doi
      })
    } else {
      logger.info('Paper not found in Semantic Scholar', {
        userId,
        correlationId,
        searchMethod,
        hasDoi: !!doi,
        hasTitle: !!title,
        hasAuthors: <AUTHORS>
      })
    }

    // STEP 3: Merge data from both sources
    if (zoteroMetadata || semanticScholarMetadata) {
      // Start with Zotero data (basic metadata like title, authors, venue, year)
      if (zoteroMetadata) {
        enrichedMetadata = { ...zoteroMetadata }
        source = zoteroLibraryInfo ? `Zotero - ${zoteroLibraryInfo.libraryName}` : 'Zotero'
      }

      // Add Semantic Scholar data (citation counts, DOI if missing)
      if (semanticScholarMetadata) {
        if (!enrichedMetadata) {
          enrichedMetadata = {}
        }

        // Always prefer Semantic Scholar for citation data
        if (semanticScholarMetadata.citationCount !== undefined) {
          enrichedMetadata.citationCount = semanticScholarMetadata.citationCount
        }
        if (semanticScholarMetadata.referenceCount !== undefined) {
          enrichedMetadata.referenceCount = semanticScholarMetadata.referenceCount
        }

        // Use Semantic Scholar DOI if Zotero doesn't have one
        if (!enrichedMetadata.doi && semanticScholarMetadata.doi) {
          enrichedMetadata.doi = semanticScholarMetadata.doi
        }

        // If we only have Semantic Scholar data, use all of it
        if (!zoteroMetadata) {
          enrichedMetadata = { ...semanticScholarMetadata }
          source = 'Semantic Scholar'
        } else {
          // Update source to indicate combined data
          source = `${source} + Semantic Scholar`
        }
      }

      logger.info('Successfully merged metadata from sources', {
        userId,
        correlationId,
        hasZoteroData: !!zoteroMetadata,
        hasSemanticScholarData: !!semanticScholarMetadata,
        finalSource: source,
        citationCount: enrichedMetadata.citationCount,
        referenceCount: enrichedMetadata.referenceCount,
        doi: enrichedMetadata.doi
      })
    }

    // STEP 4: Return results
    if (!enrichedMetadata) {
      logger.info('Paper not found in any source', { userId, correlationId, hasDoi: !!doi, hasTitle: !!title, hasAuthors: <AUTHORS>

      // Provide specific error message based on what was attempted
      let errorMessage = "Paper not found in Zotero or Semantic Scholar"

      if (!doi && title && authors && authors.length > 0) {
        // No DOI, tried title and authors matching
        errorMessage = "No matching paper found by title and authors in Semantic Scholar database"
      } else if (!doi && title) {
        // No DOI, only title available
        errorMessage = "No matching paper found by title in Semantic Scholar database. Try adding author information for better matching."
      } else if (doi) {
        // Had DOI but still no match
        errorMessage = "Paper not found in Semantic Scholar database using the provided DOI"
      }

      return NextResponse.json({
        message: errorMessage,
        enriched: false,
        metadata: {},
        source: null,
        searchDetails: {
          hasDoi: !!doi,
          hasTitle: !!title,
          hasAuthors: <AUTHORS>
          searchMethod: doi ? 'DOI' : (title ? 'title_and_authors' : 'none')
        }
      })
    }

    const response = {
      message: "Paper enriched successfully",
      enriched: true,
      metadata: enrichedMetadata,
      source
    }

    // Add Zotero library info if found in Zotero
    if (zoteroLibraryInfo) {
      response.zoteroLibrary = zoteroLibraryInfo
    }

    logger.info('Successfully enriched paper with metadata', {
      userId,
      source,
      citationCount: enrichedMetadata.citationCount,
      referenceCount: enrichedMetadata.referenceCount,
      doi: enrichedMetadata.doi,
      zoteroLibrary: zoteroLibraryInfo?.libraryName
    })

    return NextResponse.json(response)

  } catch (error: any) {
    logger.error("Critical error in paper enrichment", {
      error: error.message,
      errorName: error.name,
      errorStack: error.stack,
      userId,
      correlationId,
      title,
      doi,
      collectionId,
      requestBody: validatedData.body,
      userPreferences: user.preferences,
      timestamp: new Date().toISOString()
    })

    // Provide more specific error messages based on the error type
    let errorMessage = "Failed to enrich paper metadata"
    let statusCode = 500

    if (error.message?.includes('Zotero API error')) {
      errorMessage = "Failed to enrich paper: Zotero API error"
      statusCode = 400
      logger.error("Zotero API specific error", {
        userId,
        correlationId,
        zoteroError: error.message,
        title,
        doi,
        collectionId
      })
    } else if (error.message?.includes('validation')) {
      errorMessage = "Failed to enrich paper: Invalid request data"
      statusCode = 400
      logger.error("Validation specific error", {
        userId,
        correlationId,
        validationError: error.message,
        requestBody: validatedData.body
      })
    } else if (error.message?.includes('Collection not found')) {
      errorMessage = "Failed to enrich paper: Collection not found"
      statusCode = 404
      logger.error("Collection not found error", {
        userId,
        correlationId,
        collectionId,
        error: error.message
      })
    }

    return NextResponse.json({
      error: errorMessage,
      enriched: false,
      metadata: {},
      source: null,
      correlationId,
      details: error.message,
      debugInfo: {
        errorType: error.name,
        timestamp: new Date().toISOString(),
        hasCollectionContext: !!collectionId
      }
    }, { status: statusCode })
  }
}, {
  body: validationSchemas.paper.enrich
}, {
  allowUnverified: true,
  auditLog: {
    action: 'paper_enrich',
    resourceType: 'paper'
  }
})
