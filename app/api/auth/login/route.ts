import { NextRequest, NextResponse } from 'next/server'
import { users, userSessions, auditLogs } from '@/lib/database'
import { verifyPassword, generateToken, generateSessionTokenHash, generateTokenExpiry, sanitizeUser } from '@/lib/auth'
import { withPublicSecurity } from '@/lib/security-middleware'
import { createErrorResponse, createSuccessResponse } from '@/lib/validation'
import { validationSchemas } from '@/lib/validation-schemas'
import type { LoginRequest } from '@/lib/types'

export const POST = withPublicSecurity(async (request: NextRequest, { correlationId, validatedData, logger }) => {
  try {
    const { email, password } = validatedData.body as LoginRequest

    logger.info('Login attempt', { email })

    // Find user by email
    const user = await users.getByEmail(email.toLowerCase())
    if (!user) {
      logger.warn('Login attempt with invalid email', { email })
      return createErrorResponse(
        'Invalid credentials',
        401,
        correlationId,
        undefined,
        'Invalid email or password'
      )
    }

    // Check if user is active
    if (!user.isActive) {
      return createErrorResponse(
        'Account deactivated',
        401,
        correlationId,
        undefined,
        'Account is deactivated. Please contact support.'
      )
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash)
    if (!isPasswordValid) {
      logger.warn('Login attempt with invalid password', { userId: user.id, email: user.email })

      return createErrorResponse(
        'Invalid credentials',
        401,
        correlationId,
        undefined,
        'Invalid email or password'
      )
    }

    // Check if email is verified
    if (!user.emailVerified) {
      return createErrorResponse(
        'Email not verified',
        403,
        correlationId,
        { code: 'EMAIL_NOT_VERIFIED', email: user.email },
        'Please verify your email address before signing in. Check your inbox for a verification link.'
      )
    }

    // Generate JWT token
    const token = generateToken(user)
    const tokenHash = generateSessionTokenHash(token)
    const expiresAt = generateTokenExpiry(24) // 24 hours

    // Store session in database
    await userSessions.create({
      userId: user.id,
      tokenHash,
      expiresAt: expiresAt.toISOString(),
      ipAddress: null, // IP is logged by security middleware
      userAgent: request.headers.get('user-agent') || null,
    })

    // Update last login time and get the updated user
    const updatedUser = await users.update(user.id, {
      lastLogin: new Date().toISOString(),
    })

    logger.info('User logged in successfully', { userId: user.id, email: user.email })

    // Create response with updated user data
    const response = createSuccessResponse({
      message: 'Login successful',
      user: sanitizeUser(updatedUser || user),
      token,
      expiresAt: expiresAt.toISOString(),
    }, correlationId)

    // Set HTTP-only cookie for additional security
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/',
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'An unexpected error occurred during login'
    )
  }
}, {
  body: validationSchemas.auth.login,
  maxBodySize: 1024 // 1KB limit for login requests
}, {
  rateLimitConfig: {
    maxRequests: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
    message: 'Too many login attempts. Please try again later.'
  },
  auditLog: {
    action: 'user_login',
    resourceType: 'user',
    getResourceId: (request, routeParams) => 'login'
  }
})