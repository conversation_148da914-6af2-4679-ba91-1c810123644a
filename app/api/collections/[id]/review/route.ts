import { type NextRequest, NextResponse } from "next/server"
import { collections, reviews } from "@/lib/database"
import { withAuth, canAccessResource } from "@/lib/auth-middleware"

export const POST = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const collectionId = params.id

    // Get the collection
    const collection = await collections.getById(collectionId)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    // Check if user can access this collection
    if (!canAccessResource(user, collection.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Add all papers in the collection to review queue
    const results = await Promise.all(
      collection.paperIds.map(async (paperId) => {
        try {
          // Check if review already exists
          const existingReview = await reviews.getByPaperId(paperId)
          
          if (existingReview) {
            // Update existing review to be due immediately
            const updatedReview = await reviews.update(paperId, {
              nextDue: new Date().toISOString(),
              ease: existingReview.ease,
            })
            return { paperId, action: 'updated', review: updatedReview }
          } else {
            // Create new review entry
            const newReview = {
              paperId,
              ease: 2.5,
              nextDue: new Date().toISOString(),
            }
            
            const createdReview = await reviews.create(newReview)
            return { paperId, action: 'created', review: createdReview }
          }
        } catch (error) {
          console.error(`Error adding paper ${paperId} to review:`, error)
          return { paperId, action: 'failed', error: error.message }
        }
      })
    )

    const successful = results.filter(r => r.action !== 'failed')
    const failed = results.filter(r => r.action === 'failed')
    
    return NextResponse.json({
      message: `Collection added to review queue`,
      collection: collection.name,
      totalPapers: collection.paperIds.length,
      successful: successful.length,
      failed: failed.length,
      results
    })
  } catch (error) {
    console.error("Error adding collection to review:", error)
    return NextResponse.json({ error: "Failed to add collection to review" }, { status: 500 })
  }
}, { allowUnverified: true })

export const DELETE = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const collectionId = params.id

    // Get the collection
    const collection = await collections.getById(collectionId)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    // Check if user can access this collection
    if (!canAccessResource(user, collection.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Remove all papers in the collection from review queue
    const results = await Promise.all(
      collection.paperIds.map(async (paperId) => {
        try {
          const deleted = await reviews.delete(paperId)
          return { paperId, action: deleted ? 'removed' : 'not_found' }
        } catch (error) {
          console.error(`Error removing paper ${paperId} from review:`, error)
          return { paperId, action: 'failed', error: error.message }
        }
      })
    )

    const successful = results.filter(r => r.action === 'removed')
    const notFound = results.filter(r => r.action === 'not_found')
    const failed = results.filter(r => r.action === 'failed')
    
    return NextResponse.json({
      message: `Collection removed from review queue`,
      collection: collection.name,
      totalPapers: collection.paperIds.length,
      removed: successful.length,
      notFound: notFound.length,
      failed: failed.length,
      results
    })
  } catch (error) {
    console.error("Error removing collection from review:", error)
    return NextResponse.json({ error: "Failed to remove collection from review" }, { status: 500 })
  }
}, { allowUnverified: true })

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const collectionId = params.id
    
    // Get the collection
    const collection = await collections.getById(collectionId)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    // Get review status for all papers in the collection
    const reviewStatuses = await Promise.all(
      collection.paperIds.map(async (paperId) => {
        try {
          const review = await reviews.getByPaperId(paperId)
          if (review) {
            const now = new Date()
            const dueDate = new Date(review.nextDue)
            const isDue = dueDate <= now
            
            return {
              paperId,
              inReview: true,
              review,
              isDue,
              daysUntilDue: Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
            }
          } else {
            return {
              paperId,
              inReview: false,
              review: null,
              isDue: false,
              daysUntilDue: null
            }
          }
        } catch (error) {
          console.error(`Error getting review status for paper ${paperId}:`, error)
          return {
            paperId,
            inReview: false,
            review: null,
            isDue: false,
            daysUntilDue: null,
            error: error.message
          }
        }
      })
    )

    const inReview = reviewStatuses.filter(s => s.inReview)
    const due = reviewStatuses.filter(s => s.isDue) // Papers due now (nextDue <= now)

    // Scheduled: Papers in review system but not yet due (future reviews)
    const scheduled = reviewStatuses.filter(s => s.inReview && !s.isDue)

    // Overdue: Papers that are past due (negative days until due)
    const overdue = reviewStatuses.filter(s => s.inReview && s.isDue && s.daysUntilDue < 0)

    // Reviewed: Papers that have been reviewed and are scheduled for future (positive days until due)
    const reviewed = reviewStatuses.filter(s => s.inReview && !s.isDue && s.daysUntilDue > 0)

    return NextResponse.json({
      collection: collection.name,
      totalPapers: collection.paperIds.length,
      inReview: inReview.length,
      due: due.length,
      scheduled: scheduled.length,
      overdue: overdue.length,
      reviewed: reviewed.length,
      progress: {
        total: collection.paperIds.length,
        inReview: inReview.length,
        percentage: collection.paperIds.length > 0 ? Math.round((inReview.length / collection.paperIds.length) * 100) : 0
      },
      papers: reviewStatuses
    })
  } catch (error) {
    console.error("Error getting collection review status:", error)
    return NextResponse.json({ error: "Failed to get collection review status" }, { status: 500 })
  }
}
