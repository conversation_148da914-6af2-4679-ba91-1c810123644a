import { NextRequest, NextResponse } from 'next/server'
import { users, papers, collections, reviews, notes, auditLogs, userSessions, passwordResetTokens, emailVerificationTokens } from '@/lib/database'
import { withAdminSecurity } from '@/lib/security-middleware'
import { createErrorResponse, createSuccessResponse } from '@/lib/validation'
import { z } from 'zod'

// Schema for validating import data structure
const importDataSchema = z.object({
  metadata: z.object({
    exportedAt: z.string(),
    exportedBy: z.string(),
    version: z.string(),
    description: z.string().optional()
  }),
  data: z.object({
    users: z.array(z.any()).optional(),
    papers: z.array(z.any()).optional(),
    collections: z.array(z.any()).optional(),
    reviews: z.array(z.any()).optional(),
    notes: z.array(z.any()).optional(),
    auditLogs: z.array(z.any()).optional(),
    userSessions: z.array(z.any()).optional(),
    passwordResetTokens: z.array(z.any()).optional(),
    emailVerificationTokens: z.array(z.any()).optional()
  }),
  statistics: z.object({}).optional()
})

export const POST = withAdminSecurity(async (request: NextRequest, { correlationId, user, userId, logger }) => {
  try {
    logger.info('Admin requesting database import', { userId })

    // Parse the request body
    const body = await request.json()
    
    // Validate the import data structure
    const validationResult = importDataSchema.safeParse(body)
    if (!validationResult.success) {
      logger.warn('Invalid import data structure', { 
        userId, 
        errors: validationResult.error.errors 
      })
      return createErrorResponse('Invalid import data structure', 400, correlationId, validationResult.error.errors)
    }

    const importData = validationResult.data
    const { data } = importData

    // Track import statistics
    const importStats = {
      usersImported: 0,
      papersImported: 0,
      collectionsImported: 0,
      reviewsImported: 0,
      notesImported: 0,
      auditLogsImported: 0,
      userSessionsImported: 0,
      passwordResetTokensImported: 0,
      emailVerificationTokensImported: 0,
      errors: [] as string[]
    }

    // Import users first (since other tables reference users)
    if (data.users && data.users.length > 0) {
      try {
        for (const userData of data.users) {
          try {
            // Check if user already exists
            const existingUser = await users.getById(userData.id)
            if (!existingUser) {
              await users.create(userData)
              importStats.usersImported++
            }
          } catch (error: any) {
            importStats.errors.push(`User import error: ${error.message}`)
          }
        }
      } catch (error: any) {
        importStats.errors.push(`Users batch import error: ${error.message}`)
      }
    }

    // Import papers
    if (data.papers && data.papers.length > 0) {
      try {
        for (const paperData of data.papers) {
          try {
            // Check if paper already exists
            const existingPaper = await papers.getById(paperData.id)
            if (!existingPaper) {
              await papers.create(paperData)
              importStats.papersImported++
            }
          } catch (error: any) {
            importStats.errors.push(`Paper import error: ${error.message}`)
          }
        }
      } catch (error: any) {
        importStats.errors.push(`Papers batch import error: ${error.message}`)
      }
    }

    // Import collections
    if (data.collections && data.collections.length > 0) {
      try {
        for (const collectionData of data.collections) {
          try {
            // Check if collection already exists
            const existingCollection = await collections.getById(collectionData.id)
            if (!existingCollection) {
              await collections.create(collectionData)
              importStats.collectionsImported++
            }
          } catch (error: any) {
            importStats.errors.push(`Collection import error: ${error.message}`)
          }
        }
      } catch (error: any) {
        importStats.errors.push(`Collections batch import error: ${error.message}`)
      }
    }

    // Import reviews
    if (data.reviews && data.reviews.length > 0) {
      try {
        for (const reviewData of data.reviews) {
          try {
            // Check if review already exists
            const existingReview = await reviews.getByPaperId(reviewData.paperId)
            if (!existingReview) {
              await reviews.create(reviewData)
              importStats.reviewsImported++
            }
          } catch (error: any) {
            importStats.errors.push(`Review import error: ${error.message}`)
          }
        }
      } catch (error: any) {
        importStats.errors.push(`Reviews batch import error: ${error.message}`)
      }
    }

    // Import notes
    if (data.notes && data.notes.length > 0) {
      try {
        for (const noteData of data.notes) {
          try {
            // Check if note already exists
            const existingNote = await notes.getById(noteData.id)
            if (!existingNote) {
              await notes.create(noteData)
              importStats.notesImported++
            }
          } catch (error: any) {
            importStats.errors.push(`Note import error: ${error.message}`)
          }
        }
      } catch (error: any) {
        importStats.errors.push(`Notes batch import error: ${error.message}`)
      }
    }

    // Log the import action
    await auditLogs.create({
      userId,
      action: 'database_import',
      resourceType: 'system',
      resourceId: 'database',
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      details: {
        importStats,
        sourceMetadata: importData.metadata
      }
    })

    logger.info('Database import completed', { 
      userId, 
      importStats
    })

    return createSuccessResponse({
      message: 'Database import completed',
      importStats
    }, correlationId)

  } catch (error: any) {
    logger.error('Error importing database', { error: error.message, userId })
    return createErrorResponse('Failed to import database', 500, correlationId)
  }
})
