import { NextRequest, NextResponse } from 'next/server'
import { users, papers, collections, reviews, notes, auditLogs, userSessions, passwordResetTokens, emailVerificationTokens } from '@/lib/database'
import { withAdminSecurity } from '@/lib/security-middleware'
import { createErrorResponse } from '@/lib/validation'

export const GET = withAdminSecurity(async (request: NextRequest, { correlationId, user, userId, logger }) => {
  try {
    logger.info('Admin requesting database export', { userId })

    // Get all data from all tables
    const [
      allUsers,
      allPapers,
      allCollections,
      allReviews,
      allNotes,
      allAuditLogs,
      allUserSessions,
      allPasswordResetTokens,
      allEmailVerificationTokens
    ] = await Promise.all([
      users.getAll(),
      papers.getAll(),
      collections.getAll(),
      reviews.getAll(),
      notes.getAll(),
      auditLogs.getAll(10000), // Get a large number of audit logs
      userSessions.getAll(),
      passwordResetTokens.getAll(),
      emailVerificationTokens.getAll()
    ])

    // Create export data structure
    const exportData = {
      metadata: {
        exportedAt: new Date().toISOString(),
        exportedBy: userId,
        version: '1.0',
        description: 'PaperNugget database export'
      },
      data: {
        users: allUsers,
        papers: allPapers,
        collections: allCollections,
        reviews: allReviews,
        notes: allNotes,
        auditLogs: allAuditLogs,
        userSessions: allUserSessions,
        passwordResetTokens: allPasswordResetTokens,
        emailVerificationTokens: allEmailVerificationTokens
      },
      statistics: {
        totalUsers: allUsers.length,
        totalPapers: allPapers.length,
        totalCollections: allCollections.length,
        totalReviews: allReviews.length,
        totalNotes: allNotes.length,
        totalAuditLogs: allAuditLogs.length,
        totalUserSessions: allUserSessions.length,
        totalPasswordResetTokens: allPasswordResetTokens.length,
        totalEmailVerificationTokens: allEmailVerificationTokens.length
      }
    }

    // Log the export action
    await auditLogs.create({
      userId,
      action: 'database_export',
      resourceType: 'system',
      resourceId: 'database',
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      details: {
        exportSize: JSON.stringify(exportData).length,
        statistics: exportData.statistics
      }
    })

    logger.info('Database export completed', { 
      userId, 
      exportSize: JSON.stringify(exportData).length,
      statistics: exportData.statistics
    })

    // Return the export data as JSON
    const response = NextResponse.json(exportData)
    
    // Set headers for file download
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    response.headers.set('Content-Disposition', `attachment; filename="papernugget-backup-${timestamp}.json"`)
    response.headers.set('Content-Type', 'application/json')
    
    return response

  } catch (error: any) {
    logger.error('Error exporting database', { error: error.message, userId })
    return createErrorResponse('Failed to export database', 500, correlationId)
  }
})
