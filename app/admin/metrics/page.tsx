'use client'

import React, { useState, useEffect } from 'react'
import { ConditionalSidebarTrigger } from '@/components/ui/conditional-sidebar-trigger'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AuthModal } from '@/components/auth/AuthModal'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/hooks/use-toast'
import { authenticatedFetch } from '@/lib/utils'
import { 
  BarChart3, 
  Shield,
  Server,
  Database,
  Clock,
  TrendingUp,
  Users,
  FileText,
  BookOpen,
  Activity,
  Cpu,
  HardDrive,
  Wifi
} from 'lucide-react'
import Link from 'next/link'

interface SystemMetrics {
  database: {
    totalConnections: number
    activeConnections: number
    totalQueries: number
    avgQueryTime: number
    tableStats: {
      users: number
      papers: number
      collections: number
      reviews: number
      auditLogs: number
    }
  }
  system: {
    uptime: number
    memoryUsage: {
      used: number
      total: number
      percentage: number
    }
    cpuUsage: number
    diskUsage: {
      used: number
      total: number
      percentage: number
    }
  }
  api: {
    totalRequests: number
    requestsPerMinute: number
    averageResponseTime: number
    errorRate: number
    endpointStats: {
      [endpoint: string]: {
        requests: number
        avgResponseTime: number
        errorCount: number
      }
    }
  }
}

function AdminMetricsContent() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchMetrics()
    // Refresh metrics every 30 seconds
    const interval = setInterval(fetchMetrics, 30000)
    return () => clearInterval(interval)
  }, [])

  const fetchMetrics = async () => {
    try {
      const response = await authenticatedFetch('/api/metrics')
      if (!response.ok) {
        throw new Error('Failed to fetch metrics')
      }
      const data = await response.json()
      setMetrics(data.data)
    } catch (error) {
      console.error('Failed to fetch metrics:', error)
      toast({
        title: 'Failed to load metrics',
        description: 'Please try refreshing the page.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}d ${hours}h ${minutes}m`
  }

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Failed to load metrics</h3>
          <p className="text-muted-foreground">Please try refreshing the page.</p>
          <Button onClick={fetchMetrics} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <ConditionalSidebarTrigger />
          <div className="ml-4 flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-500" />
            <h1 className="text-lg font-semibold">System Metrics</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">System Performance</h2>
            <p className="text-muted-foreground">
              Real-time system and application metrics
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchMetrics}>
              Refresh
            </Button>
            <Link href="/admin">
              <Button variant="outline">
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>

        {/* System Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatUptime(metrics.system.uptime)}</div>
              <p className="text-xs text-muted-foreground">
                System running smoothly
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
              <Cpu className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.system.memoryUsage.percentage.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                {formatBytes(metrics.system.memoryUsage.used)} / {formatBytes(metrics.system.memoryUsage.total)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
              <Server className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.system.cpuUsage.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                Current CPU load
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.system.diskUsage.percentage.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                {formatBytes(metrics.system.diskUsage.used)} / {formatBytes(metrics.system.diskUsage.total)}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Database Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Performance
            </CardTitle>
            <CardDescription>
              Database connections, queries, and table statistics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-2">
                <div className="text-sm font-medium">Connections</div>
                <div className="text-2xl font-bold">{metrics.database.activeConnections}</div>
                <div className="text-xs text-muted-foreground">
                  {metrics.database.totalConnections} total
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium">Total Queries</div>
                <div className="text-2xl font-bold">{metrics.database.totalQueries.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">
                  Avg: {metrics.database.avgQueryTime.toFixed(2)}ms
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium">Table Sizes</div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Users:</span>
                    <span className="font-medium">{metrics.database.tableStats.users}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Papers:</span>
                    <span className="font-medium">{metrics.database.tableStats.papers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Collections:</span>
                    <span className="font-medium">{metrics.database.tableStats.collections}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Reviews:</span>
                    <span className="font-medium">{metrics.database.tableStats.reviews}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wifi className="h-5 w-5" />
              API Performance
            </CardTitle>
            <CardDescription>
              Request statistics and response times
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="space-y-2">
                <div className="text-sm font-medium">Total Requests</div>
                <div className="text-2xl font-bold">{metrics.api.totalRequests.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">
                  All time
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium">Requests/Min</div>
                <div className="text-2xl font-bold">{metrics.api.requestsPerMinute}</div>
                <div className="text-xs text-muted-foreground">
                  Current rate
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium">Avg Response</div>
                <div className="text-2xl font-bold">{metrics.api.averageResponseTime.toFixed(0)}ms</div>
                <div className="text-xs text-muted-foreground">
                  Response time
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium">Error Rate</div>
                <div className="text-2xl font-bold">{(metrics.api.errorRate * 100).toFixed(2)}%</div>
                <div className="text-xs text-muted-foreground">
                  Error percentage
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Top API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle>Top API Endpoints</CardTitle>
            <CardDescription>
              Most frequently used endpoints and their performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(metrics.api.endpointStats)
                .sort(([,a], [,b]) => b.requests - a.requests)
                .slice(0, 10)
                .map(([endpoint, stats]) => (
                  <div key={endpoint} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="text-sm font-medium">{endpoint}</div>
                      <div className="text-xs text-muted-foreground">
                        {stats.requests} requests • {stats.avgResponseTime.toFixed(0)}ms avg
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{stats.errorCount} errors</div>
                      <div className="text-xs text-muted-foreground">
                        {((stats.errorCount / stats.requests) * 100).toFixed(1)}% error rate
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

export default function AdminMetricsPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      requiredRoles={['admin']}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            window.location.href = '/login'
          }}
        />
      }
    >
      <AdminMetricsContent />
    </ProtectedRoute>
  )
}
