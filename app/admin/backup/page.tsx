"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Download, Upload, Database, AlertTriangle, CheckCircle, Info } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function AdminBackupPage() {
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importProgress, setImportProgress] = useState(0)
  const [importResult, setImportResult] = useState<any>(null)
  const { toast } = useToast()

  const handleExport = async () => {
    try {
      setIsExporting(true)
      
      const response = await fetch('/api/admin/backup/export', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Export failed')
      }

      // Get the filename from the Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition')
      const filename = contentDisposition?.match(/filename="(.+)"/)?.[1] || 'papernugget-backup.json'

      // Download the file
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "Export Successful",
        description: "Database backup has been downloaded successfully.",
      })

    } catch (error) {
      console.error('Export error:', error)
      toast({
        title: "Export Failed",
        description: "Failed to export database. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.type === 'application/json' || file.name.endsWith('.json')) {
        setImportFile(file)
        setImportResult(null)
      } else {
        toast({
          title: "Invalid File Type",
          description: "Please select a JSON file.",
          variant: "destructive",
        })
      }
    }
  }

  const handleImport = async () => {
    if (!importFile) {
      toast({
        title: "No File Selected",
        description: "Please select a backup file to import.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsImporting(true)
      setImportProgress(0)
      setImportResult(null)

      // Read the file
      const fileContent = await importFile.text()
      let importData
      
      try {
        importData = JSON.parse(fileContent)
      } catch (error) {
        throw new Error('Invalid JSON file')
      }

      // Simulate progress
      setImportProgress(25)

      const response = await fetch('/api/admin/backup/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(importData),
      })

      setImportProgress(75)

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Import failed')
      }

      setImportProgress(100)
      setImportResult(result.data)

      toast({
        title: "Import Successful",
        description: "Database has been restored successfully.",
      })

    } catch (error: any) {
      console.error('Import error:', error)
      toast({
        title: "Import Failed",
        description: error.message || "Failed to import database. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsImporting(false)
      setTimeout(() => setImportProgress(0), 2000)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Database className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Database Backup & Restore</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Export Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Export Database
            </CardTitle>
            <CardDescription>
              Download a complete backup of all database data as a JSON file.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                This will export all users, papers, collections, reviews, notes, and system data.
                The export includes sensitive information and should be stored securely.
              </AlertDescription>
            </Alert>
            
            <Button 
              onClick={handleExport} 
              disabled={isExporting}
              className="w-full"
            >
              {isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export Database
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Import Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Database
            </CardTitle>
            <CardDescription>
              Restore database from a previously exported JSON backup file.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> This will add data to the existing database. 
                Duplicate entries will be skipped, but this operation cannot be undone.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Label htmlFor="backup-file">Select Backup File</Label>
              <Input
                id="backup-file"
                type="file"
                accept=".json"
                onChange={handleFileSelect}
                disabled={isImporting}
              />
            </div>

            {importFile && (
              <div className="text-sm text-muted-foreground">
                Selected: {importFile.name} ({(importFile.size / 1024 / 1024).toFixed(2)} MB)
              </div>
            )}

            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Import Progress</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="w-full" />
              </div>
            )}

            <Button 
              onClick={handleImport} 
              disabled={!importFile || isImporting}
              className="w-full"
              variant={importFile ? "default" : "secondary"}
            >
              {isImporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Importing...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Import Database
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Import Results */}
      {importResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Import Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-medium">Successfully Imported:</h4>
                <ul className="text-sm space-y-1">
                  <li>Users: {importResult.importStats?.usersImported || 0}</li>
                  <li>Papers: {importResult.importStats?.papersImported || 0}</li>
                  <li>Collections: {importResult.importStats?.collectionsImported || 0}</li>
                  <li>Reviews: {importResult.importStats?.reviewsImported || 0}</li>
                  <li>Notes: {importResult.importStats?.notesImported || 0}</li>
                </ul>
              </div>
              
              {importResult.importStats?.errors && importResult.importStats.errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-orange-600">Errors:</h4>
                  <ul className="text-sm space-y-1 max-h-32 overflow-y-auto">
                    {importResult.importStats.errors.map((error: string, index: number) => (
                      <li key={index} className="text-orange-600">{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
