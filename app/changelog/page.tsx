'use client'

import { ConditionalSidebarTrigger } from '@/components/ui/conditional-sidebar-trigger'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Button } from '@/components/ui/button'
import { Calendar, Bug, Sparkles, Zap, Shield, Wrench, ChevronDown, ChevronRight } from 'lucide-react'
import { changelog, type ChangelogEntry, isRecentRelease } from '@/lib/changelog-data'
import { useState } from 'react'

const getChangeIcon = (type: string) => {
  switch (type) {
    case 'feature':
      return <Sparkles className="h-4 w-4 text-green-600" />
    case 'bugfix':
      return <Bug className="h-4 w-4 text-red-600" />
    case 'improvement':
      return <Zap className="h-4 w-4 text-blue-600" />
    case 'security':
      return <Shield className="h-4 w-4 text-purple-600" />
    case 'breaking':
      return <Wrench className="h-4 w-4 text-orange-600" />
    default:
      return <Sparkles className="h-4 w-4 text-gray-600" />
  }
}

const getChangeBadge = (type: string) => {
  switch (type) {
    case 'feature':
      return <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">New</Badge>
    case 'bugfix':
      return <Badge variant="destructive">Fix</Badge>
    case 'improvement':
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-100">Improved</Badge>
    case 'security':
      return <Badge variant="outline" className="border-purple-200 text-purple-800">Security</Badge>
    case 'breaking':
      return <Badge variant="destructive" className="bg-orange-100 text-orange-800 hover:bg-orange-100">Breaking</Badge>
    default:
      return <Badge variant="outline">Change</Badge>
  }
}

const getVersionBadge = (type: string) => {
  switch (type) {
    case 'major':
      return <Badge variant="default" className="bg-red-100 text-red-800 hover:bg-red-100">Major</Badge>
    case 'minor':
      return <Badge variant="default" className="bg-blue-100 text-blue-800 hover:bg-blue-100">Minor</Badge>
    case 'patch':
      return <Badge variant="outline">Patch</Badge>
    default:
      return <Badge variant="outline">Release</Badge>
  }
}

export default function ChangelogPage() {
  // State to track which versions are expanded (most recent is expanded by default)
  const [expandedVersions, setExpandedVersions] = useState<Set<string>>(
    new Set([changelog[0]?.version])
  )

  const toggleVersion = (version: string) => {
    const newExpanded = new Set(expandedVersions)
    if (newExpanded.has(version)) {
      newExpanded.delete(version)
    } else {
      newExpanded.add(version)
    }
    setExpandedVersions(newExpanded)
  }

  const expandAll = () => {
    setExpandedVersions(new Set(changelog.map(entry => entry.version)))
  }

  const collapseAll = () => {
    setExpandedVersions(new Set())
  }

  const allExpanded = expandedVersions.size === changelog.length
  const noneExpanded = expandedVersions.size === 0

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <ConditionalSidebarTrigger />
          <div className="ml-4">
            <h1 className="text-lg font-semibold">Changelog</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 container py-6">
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Changelog</h1>
              <p className="text-muted-foreground mt-2">
                Track the latest features, improvements, and bug fixes in PaperNugget.
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={allExpanded ? collapseAll : expandAll}
                className="text-sm"
              >
                {allExpanded ? 'Collapse All' : 'Expand All'}
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {changelog.map((entry, index) => {
              const isExpanded = expandedVersions.has(entry.version)

              return (
                <Card key={entry.version} className="relative">
                  <Collapsible open={isExpanded} onOpenChange={() => toggleVersion(entry.version)}>
                    <CollapsibleTrigger asChild>
                      <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2">
                              {isExpanded ? (
                                <ChevronDown className="h-4 w-4 text-muted-foreground" />
                              ) : (
                                <ChevronRight className="h-4 w-4 text-muted-foreground" />
                              )}
                              <CardTitle className="text-xl">v{entry.version}</CardTitle>
                            </div>
                            {getVersionBadge(entry.type)}
                            {isRecentRelease(entry.date) && (
                              <Badge variant="default" className="bg-green-500 hover:bg-green-500 text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            {new Date(entry.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </div>
                        </div>
                      </CardHeader>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <CardContent className="pt-0">
                        <div className="space-y-4">
                          {entry.changes.map((change, changeIndex) => (
                            <div key={changeIndex} className="flex gap-3">
                              <div className="flex-shrink-0 mt-0.5">
                                {getChangeIcon(change.type)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h3 className="font-medium">{change.title}</h3>
                                  {getChangeBadge(change.type)}
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {change.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              )
            })}
          </div>

          <Card className="border-dashed">
            <CardContent className="pt-6">
              <div className="text-center text-muted-foreground">
                <p className="text-sm">
                  Want to suggest a feature or report a bug?{' '}
                  <a
                    href="https://papernugget.canny.io/make-paper-nugget-better"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline"
                  >
                    Submit feedback
                  </a>{' '}
                  to help us improve PaperNugget.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
