"use client"

import type React from "react"
import dynamic from "next/dynamic"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Save, RefreshCw } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ConditionalSidebarTrigger } from "@/components/ui/conditional-sidebar-trigger"
import { useToast } from "@/hooks/use-toast"
import { useApiRequest, useLoggedFetch } from "@/hooks/use-logged-fetch"
import { useErrorLogger } from "@/lib/utils"
import { COMMON_PAPER_TYPES, PAPER_TYPE_LABELS, type PaperType } from "@/lib/types"

export default function NewPaperPage() {
  const router = useRouter()
  const { toast } = useToast()
  const api = useApiRequest('NewPaperPage')
  const { fetch: loggedFetch } = useLoggedFetch('NewPaperPage')
  const { logError } = useErrorLogger('NewPaperPage')
  const [formData, setFormData] = useState({
    title: "",
    authors: "",
    venue: "",
    year: "",
    doi: "",
    url: "",
    tags: "",
    paperType: "journalArticle" as PaperType,
  })
  const [isEnriching, setIsEnriching] = useState(false)
  const [isDoiMode, setIsDoiMode] = useState(true)
  const [enrichedData, setEnrichedData] = useState<any>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  // Client-side validation function
  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (isDoiMode) {
      if (!formData.doi.trim()) {
        errors.doi = "DOI is required"
      }
      if (!enrichedData) {
        errors.general = "Please fetch paper details first"
      }
    } else {
      if (!formData.title.trim()) {
        errors.title = "Title is required"
      }
      if (!formData.authors.trim()) {
        errors.authors = "At least one author is required"
      }
      if (formData.year && (isNaN(Number(formData.year)) || Number(formData.year) < 1900 || Number(formData.year) > new Date().getFullYear() + 5)) {
        errors.year = "Please enter a valid year"
      }
      if (formData.url && formData.url.trim() && !isValidUrl(formData.url)) {
        errors.url = "Please enter a valid URL"
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // URL validation helper
  const isValidUrl = (url: string) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const paperData = {
        ...formData,
        authors: formData.authors
          .split(",")
          .map((a) => a.trim())
          .filter(Boolean),
        tags: formData.tags
          .split(",")
          .map((t) => t.trim())
          .filter(Boolean),
        year: formData.year && formData.year.trim() ? Number.parseInt(formData.year) : undefined,
        // Filter out empty strings for optional fields
        venue: formData.venue.trim() || undefined,
        doi: formData.doi.trim() || undefined,
        url: formData.url.trim() || undefined,
        // Include enriched data if available (filter out null/undefined values)
        ...(enrichedData && Object.fromEntries(
          Object.entries({
            abstract: enrichedData.abstract,
            citationCount: enrichedData.citationCount,
            referenceCount: enrichedData.referenceCount,
            publicationDate: enrichedData.publicationDate,
            journal: enrichedData.journal,
            volume: enrichedData.volume,
            issue: enrichedData.issue,
            pages: enrichedData.pages,
          }).filter(([_, value]) => value != null && value !== '')
        ))
      }

      const result = await api.post<any>('/api/papers', paperData, {
        authenticated: true,
        onError: (error) => {
          logError('create_paper', error, { paperTitle: formData.title })

          // Try to parse validation errors from error message
          if (error.message.includes('400') || error.message.includes('validation')) {
            toast({
              title: "Validation failed",
              description: "Please check the form for errors",
              variant: "destructive"
            })
          } else {
            toast({
              title: "Failed to create paper",
              description: "Please check your connection and try again",
              variant: "destructive"
            })
          }
        },
        onSuccess: (responseData) => {
          // Handle wrapped response from createSuccessResponse
          const paper = responseData.data || responseData
          const hasEnrichedData = !!enrichedData
          toast({
            title: "Paper created successfully",
            description: hasEnrichedData ? "Enriched with Semantic Scholar data" : undefined
          })
          router.push(`/papers/${paper.id}`)
        }
      })
    } catch (error) {
      // Additional catch for any unexpected errors
      logError('unexpected_paper_creation_error', error, { paperTitle: formData.title })
      toast({
        title: "Failed to create paper",
        description: "Please check your connection and try again",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const fetchFromDOI = async () => {
    if (!formData.doi) return

    const result = await api.get<any>(`/api/papers/doi/${encodeURIComponent(formData.doi)}`, {
      authenticated: false,
      onError: (error) => {
        logError('fetch_from_doi', error, { doi: formData.doi })
        toast({ title: "Failed to fetch from DOI", variant: "destructive" })
      },
      onSuccess: (data) => {
        setFormData((prev) => ({
          ...prev,
          title: data.title || prev.title,
          authors: data.authors?.join(", ") || prev.authors,
        }))
        toast({ title: "Paper details fetched from DOI" })
      }
    })
  }

  const enrichFromDOI = async () => {
    if (!formData.doi.trim()) {
      toast({ title: "DOI is required for enrichment", variant: "destructive" })
      return
    }

    setIsEnriching(true)

    const result = await api.post<any>('/api/papers/enrich', {
      doi: formData.doi.trim()
    }, {
      authenticated: true,
      onError: (error) => {
        logError('enrich_paper_from_doi', error, { doi: formData.doi })
        toast({ title: "Failed to enrich paper", variant: "destructive" })
        setIsDoiMode(false)
      },
      onSuccess: (data) => {
        if (data.enriched) {
          // Store enriched data and update form
          setEnrichedData(data.metadata)

          // Auto-detect paper type from venue
          const venue = data.metadata.venue || ''
          let detectedType: PaperType = 'journalArticle'
          if (venue && (
            venue.toLowerCase().includes('conference') ||
            venue.toLowerCase().includes('symposium') ||
            venue.toLowerCase().includes('workshop') ||
            venue.toLowerCase().includes('proceedings') ||
            ['NeurIPS', 'ICML', 'ICLR', 'AAAI', 'IJCAI', 'CVPR', 'ICCV', 'ECCV', 'ACL', 'EMNLP', 'NAACL', 'SIGIR', 'WWW', 'CHI', 'UIST', 'CSCW'].includes(venue)
          )) {
            detectedType = 'conferencePaper'
          }

          setFormData(prev => ({
            ...prev,
            title: data.metadata.title || prev.title,
            authors: data.metadata.authors?.join(", ") || prev.authors,
            venue: data.metadata.venue || prev.venue,
            year: data.metadata.year?.toString() || prev.year,
            url: data.metadata.url || prev.url,
            paperType: detectedType,
          }))

          toast({
            title: "Paper found and enriched!",
            description: `Found "${data.metadata.title}" with ${data.metadata.citationCount || 0} citations`
          })

          // Switch to manual mode to allow editing
          setIsDoiMode(false)
        } else {
          toast({
            title: "Paper not found",
            description: data.message || "DOI not found in Semantic Scholar database. You can enter details manually."
          })
          setIsDoiMode(false)
        }
      }
    })

    setIsEnriching(false)
  }

  const enrichPaper = async () => {
    if (!formData.title.trim()) {
      toast({ title: "Title is required for enrichment", variant: "destructive" })
      return
    }

    setIsEnriching(true)

    const result = await api.post<any>('/api/papers/enrich', {
      title: formData.title.trim(),
      authors: formData.authors.split(",").map((author) => author.trim()).filter(Boolean),
      doi: formData.doi.trim() || undefined
    }, {
      authenticated: true,
      onError: (error) => {
        logError('enrich_paper', error, { paperTitle: formData.title })
        toast({ title: "Failed to enrich paper", variant: "destructive" })
      },
      onSuccess: (data) => {
        if (data.enriched) {
          // Update form with enriched metadata
          setEnrichedData(data.metadata)

          // Auto-detect paper type from venue if not already set to something specific
          const venue = data.metadata.venue || ''
          let detectedType: PaperType = formData.paperType
          if (formData.paperType === 'journalArticle' && venue && (
            venue.toLowerCase().includes('conference') ||
            venue.toLowerCase().includes('symposium') ||
            venue.toLowerCase().includes('workshop') ||
            venue.toLowerCase().includes('proceedings') ||
            ['NeurIPS', 'ICML', 'ICLR', 'AAAI', 'IJCAI', 'CVPR', 'ICCV', 'ECCV', 'ACL', 'EMNLP', 'NAACL', 'SIGIR', 'WWW', 'CHI', 'UIST', 'CSCW'].includes(venue)
          )) {
            detectedType = 'conferencePaper'
          }

          setFormData(prev => ({
            ...prev,
            authors: data.metadata.authors?.join(", ") || prev.authors,
            venue: data.metadata.venue || prev.venue,
            year: data.metadata.year?.toString() || prev.year,
            doi: data.metadata.doi || prev.doi,
            url: data.metadata.url || prev.url,
            paperType: detectedType,
          }))

          toast({
            title: "Paper enriched successfully",
            description: `Found ${data.metadata.citationCount || 0} citations, ${data.metadata.referenceCount || 0} references`
          })
        } else {
          toast({
            title: "No additional data found",
            description: data.message || "Paper not found in Semantic Scholar database"
          })
        }
      }
    })

    setIsEnriching(false)
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <ConditionalSidebarTrigger />
          <Link href="/papers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">New Paper</h1>
        </div>
      </header>

      <div className="flex-1 overflow-auto p-6">
        <form onSubmit={handleSubmit} className="max-w-2xl mx-auto space-y-6">
          {/* General validation error */}
          {validationErrors.general && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{validationErrors.general}</p>
            </div>
          )}
          {isDoiMode ? (
            // DOI-first mode
            <div className="space-y-6">
              <div className="text-center space-y-4">
                <h2 className="text-xl font-semibold">Add Paper from DOI</h2>
                <p className="text-muted-foreground">
                  Enter a DOI to automatically fetch paper details from Semantic Scholar
                </p>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="doi">DOI</Label>
                  <Input
                    id="doi"
                    value={formData.doi}
                    onChange={(e) => {
                      setFormData((prev) => ({ ...prev, doi: e.target.value }))
                      // Clear validation error when user starts typing
                      if (validationErrors.doi) {
                        setValidationErrors(prev => ({ ...prev, doi: '' }))
                      }
                    }}
                    placeholder="10.1000/182"
                    className={`text-lg ${validationErrors.doi ? 'border-red-500' : ''}`}
                  />
                  {validationErrors.doi && (
                    <p className="text-sm text-red-600">{validationErrors.doi}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="paperType">Paper Type</Label>
                  <Select
                    value={formData.paperType}
                    onValueChange={(value: PaperType) =>
                      setFormData((prev) => ({ ...prev, paperType: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select paper type" />
                    </SelectTrigger>
                    <SelectContent>
                      {COMMON_PAPER_TYPES.map((type) => (
                        <SelectItem key={type} value={type}>
                          {PAPER_TYPE_LABELS[type]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex gap-2">
                  <Button
                    type="button"
                    onClick={enrichFromDOI}
                    disabled={!formData.doi.trim() || isEnriching}
                    className="flex-1"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${isEnriching ? 'animate-spin' : ''}`} />
                    {isEnriching ? 'Fetching...' : 'Fetch Paper Details'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDoiMode(false)}
                  >
                    Enter Manually
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // Manual entry mode
            <>
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Paper Details</h2>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsDoiMode(true)}
                >
                  Back to DOI Entry
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor="doi">DOI (optional)</Label>
                <Input
                  id="doi"
                  value={formData.doi}
                  onChange={(e) => setFormData((prev) => ({ ...prev, doi: e.target.value }))}
                  placeholder="10.1000/182"
                />
              </div>

          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Textarea
              id="title"
              value={formData.title}
              onChange={(e) => {
                setFormData((prev) => ({ ...prev, title: e.target.value }))
                // Clear validation error when user starts typing
                if (validationErrors.title) {
                  setValidationErrors(prev => ({ ...prev, title: '' }))
                }
              }}
              placeholder="Enter paper title..."
              required
              rows={3}
              className={validationErrors.title ? 'border-red-500' : ''}
            />
            {validationErrors.title && (
              <p className="text-sm text-red-600">{validationErrors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="authors">Authors *</Label>
            <Textarea
              id="authors"
              value={formData.authors}
              onChange={(e) => {
                setFormData((prev) => ({ ...prev, authors: e.target.value }))
                // Clear validation error when user starts typing
                if (validationErrors.authors) {
                  setValidationErrors(prev => ({ ...prev, authors: '' }))
                }
              }}
              placeholder="Author 1, Author 2, Author 3..."
              required
              rows={2}
              className={validationErrors.authors ? 'border-red-500' : ''}
            />
            {validationErrors.authors && (
              <p className="text-sm text-red-600">{validationErrors.authors}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="paperType">Paper Type</Label>
            <Select
              value={formData.paperType}
              onValueChange={(value: PaperType) =>
                setFormData((prev) => ({ ...prev, paperType: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select paper type" />
              </SelectTrigger>
              <SelectContent>
                {COMMON_PAPER_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {PAPER_TYPE_LABELS[type]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="venue">Venue</Label>
              <Input
                id="venue"
                value={formData.venue}
                onChange={(e) => setFormData((prev) => ({ ...prev, venue: e.target.value }))}
                placeholder="Conference/Journal"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="year">Year</Label>
              <Input
                id="year"
                type="number"
                value={formData.year}
                onChange={(e) => setFormData((prev) => ({ ...prev, year: e.target.value }))}
                placeholder="2024"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="url">URL</Label>
            <Input
              id="url"
              type="url"
              value={formData.url}
              onChange={(e) => setFormData((prev) => ({ ...prev, url: e.target.value }))}
              placeholder="https://..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData((prev) => ({ ...prev, tags: e.target.value }))}
              placeholder="machine learning, nlp, transformers..."
            />
          </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={enrichPaper}
                  disabled={isEnriching || !formData.title.trim()}
                  className="flex-1"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isEnriching ? 'animate-spin' : ''}`} />
                  Enrich from Semantic Scholar
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={!formData.title.trim() || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Create Paper
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Create button for DOI mode */}
          {isDoiMode && enrichedData && (
            <Button
              type="submit"
              className="w-full"
              size="lg"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Paper: {enrichedData.title}
                </>
              )}
            </Button>
          )}
        </form>
      </div>
    </div>
  )
}

// Force dynamic rendering to avoid SSR issues with authentication
export const dynamic = 'force-dynamic'
