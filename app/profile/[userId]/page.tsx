'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { User, Calendar, BookOpen, ExternalLink, Star, Tag } from 'lucide-react'
import { loggedFetch } from '@/lib/utils'
import type { Paper } from '@/lib/types'

interface PublicProfile {
  id: string
  displayName: string
  createdAt: string
  paperCount: number
  papers: Paper[]
}

export default function PublicProfilePage() {
  const params = useParams()
  const userId = params.userId as string
  const { toast } = useToast()
  
  const [profile, setProfile] = useState<PublicProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchProfile()
  }, [userId])

  const fetchProfile = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await loggedFetch(`/api/profile/${userId}`)
      
      if (response.ok) {
        const data = await response.json()
        setProfile(data.data)
      } else if (response.status === 404) {
        setError('User not found')
      } else if (response.status === 403) {
        setError('This profile is private')
      } else {
        setError('Failed to load profile')
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
      setError('Failed to load profile')
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const openPaperUrl = (paper: Paper) => {
    if (paper.url) {
      window.open(paper.url, '_blank')
    } else if (paper.doi) {
      window.open(`https://doi.org/${paper.doi}`, '_blank')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-muted rounded w-1/3"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-32 bg-muted rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="space-y-4">
              <User className="h-16 w-16 mx-auto text-muted-foreground" />
              <h1 className="text-2xl font-bold text-muted-foreground">{error}</h1>
              <p className="text-muted-foreground">
                {error === 'This profile is private' 
                  ? 'The user has chosen to keep their profile private.'
                  : 'Please check the URL and try again.'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return null
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Profile Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-8 w-8 text-primary" />
                </div>
                <div className="space-y-1">
                  <CardTitle className="text-2xl">{profile.displayName}</CardTitle>
                  <CardDescription className="flex items-center space-x-4">
                    <span className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>Joined {formatDate(profile.createdAt)}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <BookOpen className="h-4 w-4" />
                      <span>{profile.paperCount} papers</span>
                    </span>
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Papers Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5" />
                <span>Research Papers ({profile.paperCount})</span>
              </CardTitle>
              <CardDescription>
                Papers shared by {profile.displayName}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {profile.papers.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No papers shared yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {profile.papers.map((paper) => (
                    <div key={paper.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1 flex-1">
                          <h3 className="font-semibold text-lg leading-tight">{paper.title}</h3>
                          {paper.authors && paper.authors.length > 0 && (
                            <p className="text-sm text-muted-foreground">
                              {paper.authors.join(', ')}
                            </p>
                          )}
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            {paper.venue && <span>{paper.venue}</span>}
                            {paper.year && <span>{paper.year}</span>}
                            {paper.journal && <span>{paper.journal}</span>}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {paper.starred && (
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          )}
                          {(paper.url || paper.doi) && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openPaperUrl(paper)}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      {paper.abstract && (
                        <div>
                          <p className="text-sm text-muted-foreground line-clamp-3">
                            {paper.abstract}
                          </p>
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                          {paper.citationCount !== null && paper.citationCount !== undefined && (
                            <span>Citations: {paper.citationCount}</span>
                          )}
                          {paper.referenceCount !== null && paper.referenceCount !== undefined && (
                            <span>References: {paper.referenceCount}</span>
                          )}
                          {paper.paperType && (
                            <Badge variant="outline" className="text-xs">
                              {paper.paperType}
                            </Badge>
                          )}
                        </div>
                        
                        {paper.tags && paper.tags.length > 0 && (
                          <div className="flex items-center space-x-1">
                            <Tag className="h-3 w-3 text-muted-foreground" />
                            <div className="flex flex-wrap gap-1">
                              {paper.tags.slice(0, 3).map((tag, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {paper.tags.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{paper.tags.length - 3}
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
