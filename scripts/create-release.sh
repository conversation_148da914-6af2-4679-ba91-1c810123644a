#!/bin/bash

# PaperNugget Release Creation Script
# This script helps create GitHub releases that match our changelog versioning

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "This script must be run from within a git repository"
    exit 1
fi

# Check if we're on main branch
current_branch=$(git branch --show-current)
if [ "$current_branch" != "main" ]; then
    print_warning "You are on branch '$current_branch', not 'main'"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check for uncommitted changes
if ! git diff-index --quiet HEAD --; then
    print_error "You have uncommitted changes. Please commit or stash them first."
    exit 1
fi

# Get the latest version from changelog
print_status "Reading latest version from changelog..."
if [ ! -f "lib/changelog-data.ts" ]; then
    print_error "Changelog data file not found: lib/changelog-data.ts"
    exit 1
fi

# Extract version from changelog data
latest_version=$(grep -m 1 "version:" lib/changelog-data.ts | sed "s/.*version: '\([^']*\)'.*/\1/")
if [ -z "$latest_version" ]; then
    print_error "Could not extract version from changelog"
    exit 1
fi

print_status "Latest version in changelog: v$latest_version"

# Check if tag already exists
if git tag --list | grep -q "^v$latest_version$"; then
    print_warning "Tag v$latest_version already exists"
    read -p "Do you want to delete and recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git tag -d "v$latest_version"
        git push origin ":refs/tags/v$latest_version" 2>/dev/null || true
    else
        print_error "Aborting release creation"
        exit 1
    fi
fi

# Extract release notes from changelog
print_status "Extracting release notes from changelog..."
release_notes_file=$(mktemp)

# Create release notes from changelog
cat > "$release_notes_file" << EOF
# Release v$latest_version

## What's New

EOF

# Extract changes for this version from changelog (this is a simplified extraction)
# In a real implementation, you might want to parse the TypeScript more carefully
grep -A 20 "version: '$latest_version'" lib/changelog-data.ts | \
    grep -E "(type: '|title: '|description: ')" | \
    while IFS= read -r line; do
        if [[ $line =~ type:\ \'([^\']*)\' ]]; then
            change_type="${BASH_REMATCH[1]}"
            case $change_type in
                "feature") echo "### ✨ New Features" >> "$release_notes_file" ;;
                "bugfix") echo "### 🐛 Bug Fixes" >> "$release_notes_file" ;;
                "improvement") echo "### ⚡ Improvements" >> "$release_notes_file" ;;
                "security") echo "### 🛡️ Security" >> "$release_notes_file" ;;
                "breaking") echo "### 🔧 Breaking Changes" >> "$release_notes_file" ;;
            esac
        elif [[ $line =~ title:\ \'([^\']*)\' ]]; then
            title="${BASH_REMATCH[1]}"
        elif [[ $line =~ description:\ \'([^\']*)\' ]]; then
            description="${BASH_REMATCH[1]}"
            echo "- **$title**: $description" >> "$release_notes_file"
        fi
    done

# Show preview
print_status "Release notes preview:"
echo "----------------------------------------"
cat "$release_notes_file"
echo "----------------------------------------"

# Confirm creation
read -p "Create release v$latest_version? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    rm "$release_notes_file"
    print_error "Release creation cancelled"
    exit 1
fi

# Create and push tag
print_status "Creating tag v$latest_version..."
git tag -a "v$latest_version" -F "$release_notes_file"

print_status "Pushing tag to GitHub..."
git push origin "v$latest_version"

print_success "Tag v$latest_version created and pushed successfully!"
print_status "You can now create a GitHub release manually using this tag"
print_status "Or use the GitHub CLI: gh release create v$latest_version --notes-file $release_notes_file"

# Cleanup
rm "$release_notes_file"

print_success "Release creation completed!"
